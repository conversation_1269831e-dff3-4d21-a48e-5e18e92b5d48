# Global settings (optional)
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /var/run/nginx.pid;

# Events context (required)
events {
    worker_connections 1024;
}

# HTTP context
http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    access_log /var/log/nginx/access.log;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
    send_timeout 300s;

    # Server block(s) for handling requests
    server {
        listen 80;
        server_name localhost;

        location /api/ai {
            proxy_pass http://host.docker.internal:9011;
        }

        location /api/auth {
            proxy_pass http://host.docker.internal:9001;
        }

        location /api/calculation {
            proxy_pass http://host.docker.internal:9002;
        }

        location /api/config {
            proxy_pass http://host.docker.internal:10001;
        }

        location /api/coordinator {
            proxy_pass http://host.docker.internal:10004;
        }

        location /api/document-service {
            proxy_pass http://host.docker.internal:9003;
        }

        location /api/document-template {
            proxy_pass http://host.docker.internal:9009;
        }

        location /api/document-template-helper {
            proxy_pass http://host.docker.internal:9012;
        }

        location /api/image-processing {
            proxy_pass http://host.docker.internal:9010;
        }

        location /api/license {
            proxy_pass http://host.docker.internal:9004;
        }

        location /api/notification {
            proxy_pass http://host.docker.internal:9005;
        }

        location /api/ocr {
            proxy_pass http://host.docker.internal:9008;
        }

        location /api/payment {
            proxy_pass http://host.docker.internal:9006;
        }

        location /api/report {
            proxy_pass http://host.docker.internal:9007;
        }

        location /api/support {
            proxy_pass http://host.docker.internal:9013;
        }

        location /api/support/ws/ {
            proxy_pass http://host.docker.internal:9013;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
