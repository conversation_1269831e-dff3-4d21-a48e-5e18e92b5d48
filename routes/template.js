const easy_temp = require("easy-template-x");
const express = require("express");
const router = express.Router();
const { createResolver } = require("easy-template-x-angular-expressions");

/**
 * @swagger
 * tags:
 *   name: Template Processing
 *   description: API for processing templates
 */

/**
 * @swagger
 * /template:
 *   post:
 *     summary: Process template
 *     tags: [Template Processing]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: The template file to process
 *               data:
 *                 type: string
 *                 description: JSON data to merge with the template
 *             required:
 *               - file
 *               - data
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *               description: The processed template file as a binary stream
 *       400:
 *         description: Bad Request
 *       500:
 *         description: Internal Server Error
 */

router.get("/", async (req, res) => {
    res.send("Service is up");
});

router.post("/", async (req, res) => {
    const { file, data } = req.body;

    if (!file || !data) {
        res.status(400).send("File or data is missing");
        return;
    }

    let jsonData;
    try {
        jsonData = JSON.parse(data);
        console.log(jsonData);
    } catch {
        console.log(data);
        res.status(400).send("Data is not valid JSON");
        return;
    }
    const inputFile = Buffer.from(file, "base64");
    const handler = new easy_temp.TemplateHandler({
        scopeDataResolver: createResolver(),
    });

    await handler
        .process(inputFile, dataImageToBuffer(jsonData))
        .then((result) => {
            console.log("processing file. . .");
            res.status(200).send(result);
        })
        .catch((error) => {
            console.error("error occurred while working with template file " + error);
            res.status(500).send(error);
        });
});
const dataImageToBuffer = (data) => {
    const newData = { ...data };
    const traverseAndConvert = (obj) => {
        if (!obj) return; // Handle null or undefined objects
        for (const key in obj) {
            if (typeof obj[key] === "object") {
                if (obj[key] && obj[key]._type === "image" && obj[key].source) {
                    obj[key].source = Buffer.from(obj[key].source, "base64");
                } else {
                    traverseAndConvert(obj[key]);
                }
            }
        }
    };
    traverseAndConvert(newData);
    return newData;
};

module.exports = router;
