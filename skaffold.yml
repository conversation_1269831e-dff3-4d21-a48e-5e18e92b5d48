apiVersion: skaffold/v4beta1
kind: Config
metadata:
    name: backend
build:
  artifacts:
    - image: service_ocr
      context: ../Service_OCR
      docker:
        dockerfile: Dockerfile

manifests:
  rawYaml:
    - deployment.yml

profiles:
  # Local cluster - we build and deploy things locally
  - name: local
    build:
      local:
        push: false
    activation:
      - kubeContext: docker-desktop
    deploy:
      kubectl:
        defaultNamespace: backend
