<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Request Notification</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }

        .email-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .email-header {
            background-color: #4a90e2;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .email-content {
            padding: 30px;
        }

        .section-title {
            color: #4a90e2;
            border-bottom: 2px solid #4a90e2;
            padding-bottom: 10px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        ul li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }

        ul li::before {
            content: '•';
            color: #4a90e2;
            position: absolute;
            left: 0;
            font-weight: bold;
        }

        a {
            color: #4a90e2;
            text-decoration: none;
        }

        .additional-info {
            background-color: #f9f9f9;
            border-left: 4px solid #4a90e2;
            padding: 15px;
            margin-top: 20px;
        }
    </style>
</head>

<body>
    <div class="email-container">
        <div class="email-header">
            <h1>New ClerkXpress Demo Request</h1>
        </div>
        <div class="email-content">
            <p>Dear Sales Team,</p>

            <h2 class="section-title">Requester Details</h2>
            <ul>
                <li>Name: {firstName} {lastName}</li>
                <li>Job Title: {jobTitle}</li>
                <li>Email: <a href="mailto:{email}">{email}</a></li>
                <li>Phone: <a href="tel:{phone}">{phone}</a></li>
            </ul>

            <h2 class="section-title">Location Information</h2>
            <ul>
                <li>City: {cityName}</li>
                <li>State: {state}</li>
                <li>Population Size: {populationSize}</li>
            </ul>

            <h2 class="section-title">Interested Services</h2>
            {services}

            <div class="additional-info">
                <h2 class="section-title">Additional Information</h2>
                <p>{additionalInfo}</p>
            </div>
        </div>
    </div>
</body>

</html>