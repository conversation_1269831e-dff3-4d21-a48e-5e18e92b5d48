<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>8f4283cb-9f4e-4293-81c6-9b09cb00b627</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	<DockerfileRunArguments>-p 9008:9008</DockerfileRunArguments>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="DogLicense.png" />
    <None Remove="tessdata\eng.traineddata" />
    <None Remove="tessdata\osd.traineddata" />
    <None Remove="Vaccination.png" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="DogLicense.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="tessdata\eng.traineddata">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="tessdata\osd.traineddata">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Vaccination.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.6" />
    <PackageReference Include="Magick.NET-Q16-AnyCPU" Version="13.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.1" />
    <PackageReference Include="Tesseract" Version="5.2.0" />
  </ItemGroup>

</Project>
