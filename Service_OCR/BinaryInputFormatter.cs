using Microsoft.AspNetCore.Mvc.Formatters;

namespace Service_OCR
{
    public class BinaryInputFormatter : InputFormatter
    {
        public BinaryInputFormatter()
        {
            SupportedMediaTypes.Add("application/octet-stream");
        }

        public override async Task<InputFormatterResult> ReadRequestBodyAsync(InputFormatterContext context)
        {
            using (MemoryStream ms = new MemoryStream())
            {
                await context.HttpContext.Request.Body.CopyToAsync(ms);
                object result = ms.ToArray();
                return await InputFormatterResult.SuccessAsync(result);
            }
        }

        protected override bool CanReadType(Type type)
        {
            return type == typeof(byte[]);
        }
    }
}
