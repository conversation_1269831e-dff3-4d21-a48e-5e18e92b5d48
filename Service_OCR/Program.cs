using Service_OCR;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddMvc(options =>
{
    options.InputFormatters.Insert(0, new BinaryInputFormatter());
});
builder.Services.AddSwaggerGen();

var app = builder.Build();

//order 1
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/api/ocr/swagger-ui/v1/swagger.json", "Service_OCR V1");
    c.RoutePrefix = "api/ocr/swagger-ui";
});

//order 2
app.UsePathBase(new PathString("/api/ocr"));

//order 3
app.UseSwagger(c =>
{
    c.RouteTemplate = "swagger-ui/{documentname}/swagger.json";
});

app.UseRouting();

// Configure the HTTP request pipeline.
//app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Run();
