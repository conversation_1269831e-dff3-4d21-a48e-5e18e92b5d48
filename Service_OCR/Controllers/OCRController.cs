using ImageMagick;
using Microsoft.AspNetCore.Mvc;
using Tesseract;

namespace ocr_service.Controllers;

[ApiController]
public class OCRController : ControllerBase
{

    private readonly ILogger<OCRController> _logger;
    private readonly AsyncLocal<TesseractEngine> _tesseractEngine;

    public OCRController(ILogger<OCRController> logger)
    {
        _logger = logger;
        _tesseractEngine = new AsyncLocal<TesseractEngine>();
    }

    private TesseractEngine GetTesseractEngine()
    {
        var engine = _tesseractEngine.Value;
        if (engine == null)
        {
            engine = new TesseractEngine(@"./tessdata", "eng", EngineMode.Default);
            _tesseractEngine.Value = engine;
        }
        return engine;
    }

    [HttpPost("extractTextFromImage")]
    public IActionResult ExtractTextFromImageFile(IFormFile imageFile)
    {
        if (imageFile == null || imageFile.Length == 0)
        {
            return BadRequest("imageFile is required.");
        }

        string[] allowedExtensions = { ".jpg", ".jpeg", ".png" };
        var fileExtension = Path.GetExtension(imageFile.FileName).ToLower();

        if (!allowedExtensions.Contains(fileExtension))
        {
            return BadRequest("Invalid file type. Only JPG, JPEG, and PNG files are allowed.");
        }

        using var engine = GetTesseractEngine();
        using var stream = imageFile.OpenReadStream();
        using var memoryStream = new MemoryStream();
        stream.CopyTo(memoryStream);
        var bytes = memoryStream.ToArray();
        using var img = Pix.LoadFromMemory(bytes);
        using var page = engine.Process(img);

        var text = page.GetText().Trim();
        return Ok(text);
    }

    [HttpPost("extractTextFromByteArray")]
    public IActionResult ExtractTextFromByteArray(byte[] imageByteArray)
    {
        if (imageByteArray == null || imageByteArray.Length == 0)
        {
            return BadRequest("imageByteArray is required.");
        }

        using var engine = GetTesseractEngine();
        using var img = Pix.LoadFromMemory(imageByteArray);
        using var page = engine.Process(img);

        var text = page.GetText().Trim();
        return Ok(text);
    }
}
