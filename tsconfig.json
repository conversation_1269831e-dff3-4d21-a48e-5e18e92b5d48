{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/lib/configuration", "src/app/(admin)/templateBuilder/documentBuilder", "src/services/UserServices.ts", "src/components/ProtectedRoute", "src/app/(realm)/(protected)/(app)/support/issues/[issueId]/page.tsx", ".next/types/app/(realm)/(protected)/(checkout)/cart/error/page.ts", ".next/types/app/(realm)/(protected)/(checkout)/cart/success/page.ts"], "exclude": ["node_modules"]}