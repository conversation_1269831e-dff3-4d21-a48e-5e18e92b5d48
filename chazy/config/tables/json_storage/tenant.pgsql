UPDATE config.json_storage
SET json_data = $$
{
  "adminCity": "Chazy",
  "clerkName": "<PERSON>",
  "adminState": "NY",
  "clerkEmail": "<EMAIL>",
  "clerkTitle": "Town Clerk / Tax Collector",
  "adminOffice": "Chazy Town Clerk",
  "adminStreet": "PO Box 219",
  "templateKey": "dogLicenseForm",
  "adminZipCode": "12921",
  "clerkSignature": "",
  "clerkXpressUrl": "https://dev.clerkxpress.com",
  "adminOfficeRoom": "",
  "clerkPhoneNumber": "(*************",
  "cityClerkOfficeName": "Chazy Town Hall",
  "publisher": "Town Clerk"
}
$$::jsonb, 
last_modified_date = CURRENT_TIMESTAMP
WHERE properties = '{"config": "tenant"}'::jsonb;