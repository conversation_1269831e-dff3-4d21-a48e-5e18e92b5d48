INSERT INTO config.json_storage (
  json_storage_uuid,
  properties,
  created_by,
  created_date,
  last_modified_by,
  json_data,
  last_modified_date
)
VALUES (
  'ce4684b8-da44-4afb-94ac-f4085cc9e1d9',
  '{"onlineProfile": "individual-profile-builder"}'::jsonb,
  'service-account-clerkxpress-seed', 
  CURRENT_TIMESTAMP, 
  'service-account-clerkxpress-seed',
  '{
    "id": "individual-profile-builder",
    "title": "Profile Configuration",
    "profile": {
        "tab": "Profile",
        "icon": "PawPrint",
        "groups": [
            {
                "order": 1,
                "title": "Personal Information",
                "fields": [
                    {
                        "id": "firstName",
                        "span": "1/2",
                        "type": "text",
                        "label": "First Name",
                        "required": true,
                        "flaggable": true
                    },
                    {
                        "id": "middleName",
                        "span": "1/2",
                        "type": "text",
                        "label": "Middle Name",
                        "required": true,
                        "flaggable": true
                    },
                    {
                        "id": "lastName",
                        "span": "1/2",
                        "type": "text",
                        "label": "Last Name",
                        "required": true,
                        "flaggable": true
                    },
                    {
                        "id": "dateOfBirth",
                        "span": "1/2",
                        "type": "date",
                        "label": "Date of Birth",
                        "required": true,
                        "flaggable": true,
                        "formatPattern": "MMMM dd, yyyy"
                    },
                    {
                        "id": "senior",
                        "span": "1/2",
                        "type": "display",
                        "label": "Senior",
                        "computed": "isSenior",
                        "required": false,
                        "flaggable": false,
                        "computedParams": [
                            "dateOfBirth"
                        ]
                    },
                    {
                        "id": "phone",
                        "span": "full",
                        "type": "phone",
                        "label": "Phone",
                        "required": true,
                        "flaggable": true
                    },
                    {
                        "id": "email",
                        "span": "full",
                        "type": "text",
                        "label": "Email",
                        "required": true,
                        "flaggable": true
                    }
                ],
                "description": "Personal Information about the individual"
            },
            {
                "order": 2,
                "title": "Address Information",
                "fields": [
                    {
                        "id": "address",
                        "span": "1/2",
                        "type": "text",
                        "label": "Street Address",
                        "required": true,
                        "flaggable": true
                    },
                    {
                        "id": "address2",
                        "span": "1/2",
                        "type": "text",
                        "label": "Apt, Suite, etc.",
                        "required": false,
                        "flaggable": true
                    },
                    {
                        "id": "city",
                        "span": "1/2",
                        "type": "text",
                        "label": "City",
                        "required": true,
                        "flaggable": true
                    },
                    {
                        "id": "state",
                        "span": "1/2",
                        "type": "select",
                        "label": "State",
                        "options": [
                            {
                                "label": "AL",
                                "value": "AL"
                            },
                            {
                                "label": "AK",
                                "value": "AK"
                            },
                            {
                                "label": "AZ",
                                "value": "AZ"
                            },
                            {
                                "label": "AR",
                                "value": "AR"
                            },
                            {
                                "label": "CA",
                                "value": "CA"
                            },
                            {
                                "label": "CO",
                                "value": "CO"
                            },
                            {
                                "label": "CT",
                                "value": "CT"
                            },
                            {
                                "label": "DE",
                                "value": "DE"
                            },
                            {
                                "label": "FL",
                                "value": "FL"
                            },
                            {
                                "label": "GA",
                                "value": "GA"
                            },
                            {
                                "label": "HI",
                                "value": "HI"
                            },
                            {
                                "label": "ID",
                                "value": "ID"
                            },
                            {
                                "label": "IL",
                                "value": "IL"
                            },
                            {
                                "label": "IN",
                                "value": "IN"
                            },
                            {
                                "label": "IA",
                                "value": "IA"
                            },
                            {
                                "label": "KS",
                                "value": "KS"
                            },
                            {
                                "label": "KY",
                                "value": "KY"
                            },
                            {
                                "label": "LA",
                                "value": "LA"
                            },
                            {
                                "label": "ME",
                                "value": "ME"
                            },
                            {
                                "label": "MD",
                                "value": "MD"
                            },
                            {
                                "label": "MA",
                                "value": "MA"
                            },
                            {
                                "label": "MI",
                                "value": "MI"
                            },
                            {
                                "label": "MN",
                                "value": "MN"
                            },
                            {
                                "label": "MS",
                                "value": "MS"
                            },
                            {
                                "label": "MO",
                                "value": "MO"
                            },
                            {
                                "label": "MT",
                                "value": "MT"
                            },
                            {
                                "label": "NE",
                                "value": "NE"
                            },
                            {
                                "label": "NV",
                                "value": "NV"
                            },
                            {
                                "label": "NH",
                                "value": "NH"
                            },
                            {
                                "label": "NJ",
                                "value": "NJ"
                            },
                            {
                                "label": "NM",
                                "value": "NM"
                            },
                            {
                                "label": "NY",
                                "value": "NY"
                            },
                            {
                                "label": "NC",
                                "value": "NC"
                            },
                            {
                                "label": "ND",
                                "value": "ND"
                            },
                            {
                                "label": "OH",
                                "value": "OH"
                            },
                            {
                                "label": "OK",
                                "value": "OK"
                            },
                            {
                                "label": "OR",
                                "value": "OR"
                            },
                            {
                                "label": "PA",
                                "value": "PA"
                            },
                            {
                                "label": "RI",
                                "value": "RI"
                            },
                            {
                                "label": "SC",
                                "value": "SC"
                            },
                            {
                                "label": "SD",
                                "value": "SD"
                            },
                            {
                                "label": "TN",
                                "value": "TN"
                            },
                            {
                                "label": "TX",
                                "value": "TX"
                            },
                            {
                                "label": "UT",
                                "value": "UT"
                            },
                            {
                                "label": "VT",
                                "value": "VT"
                            },
                            {
                                "label": "VA",
                                "value": "VA"
                            },
                            {
                                "label": "WA",
                                "value": "WA"
                            },
                            {
                                "label": "WV",
                                "value": "WV"
                            },
                            {
                                "label": "WI",
                                "value": "WI"
                            },
                            {
                                "label": "WY",
                                "value": "WY"
                            }
                        ],
                        "required": true,
                        "flaggable": true
                    },
                    {
                        "id": "zip",
                        "span": "1/2",
                        "type": "text",
                        "label": "Zip",
                        "required": true,
                        "flaggable": false
                    },
                    {
                        "id": "mailingSameAsPrimary",
                        "span": "full",
                        "type": "boolean",
                        "label": "Mailing Address is the same",
                        "options": [
                            {
                                "label": "Yes",
                                "value": "Yes"
                            },
                            {
                                "label": "No",
                                "value": "No"
                            }
                        ],
                        "required": true,
                        "flaggable": false
                    },
                    {
                        "id": "mailaddress",
                        "span": "1/2",
                        "type": "text",
                        "label": "Mail Street Address",
                        "required": {
                            "conditions": [
                                {
                                    "field": "mailingSameAsPrimary",
                                    "value": false,
                                    "comparison": "equals"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    },
                    {
                        "id": "mailaddress2",
                        "span": "1/2",
                        "type": "text",
                        "label": "Mail Apt, Suite, etc.",
                        "required": {
                            "conditions": [
                                {
                                    "field": "mailingSameAsPrimary",
                                    "value": false,
                                    "comparison": "equals"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    },
                    {
                        "id": "mailcity",
                        "span": "1/2",
                        "type": "text",
                        "label": "MailCity",
                        "required": {
                            "conditions": [
                                {
                                    "field": "mailingSameAsPrimary",
                                    "value": false,
                                    "comparison": "equals"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    },
                    {
                        "id": "mailstate",
                        "span": "1/2",
                        "type": "select",
                        "label": "Mail State",
                        "options": [
                            {
                                "label": "AL",
                                "value": "AL"
                            },
                            {
                                "label": "AK",
                                "value": "AK"
                            },
                            {
                                "label": "AZ",
                                "value": "AZ"
                            },
                            {
                                "label": "AR",
                                "value": "AR"
                            },
                            {
                                "label": "CA",
                                "value": "CA"
                            },
                            {
                                "label": "CO",
                                "value": "CO"
                            },
                            {
                                "label": "CT",
                                "value": "CT"
                            },
                            {
                                "label": "DE",
                                "value": "DE"
                            },
                            {
                                "label": "FL",
                                "value": "FL"
                            },
                            {
                                "label": "GA",
                                "value": "GA"
                            },
                            {
                                "label": "HI",
                                "value": "HI"
                            },
                            {
                                "label": "ID",
                                "value": "ID"
                            },
                            {
                                "label": "IL",
                                "value": "IL"
                            },
                            {
                                "label": "IN",
                                "value": "IN"
                            },
                            {
                                "label": "IA",
                                "value": "IA"
                            },
                            {
                                "label": "KS",
                                "value": "KS"
                            },
                            {
                                "label": "KY",
                                "value": "KY"
                            },
                            {
                                "label": "LA",
                                "value": "LA"
                            },
                            {
                                "label": "ME",
                                "value": "ME"
                            },
                            {
                                "label": "MD",
                                "value": "MD"
                            },
                            {
                                "label": "MA",
                                "value": "MA"
                            },
                            {
                                "label": "MI",
                                "value": "MI"
                            },
                            {
                                "label": "MN",
                                "value": "MN"
                            },
                            {
                                "label": "MS",
                                "value": "MS"
                            },
                            {
                                "label": "MO",
                                "value": "MO"
                            },
                            {
                                "label": "MT",
                                "value": "MT"
                            },
                            {
                                "label": "NE",
                                "value": "NE"
                            },
                            {
                                "label": "NV",
                                "value": "NV"
                            },
                            {
                                "label": "NH",
                                "value": "NH"
                            },
                            {
                                "label": "NJ",
                                "value": "NJ"
                            },
                            {
                                "label": "NM",
                                "value": "NM"
                            },
                            {
                                "label": "NY",
                                "value": "NY"
                            },
                            {
                                "label": "NC",
                                "value": "NC"
                            },
                            {
                                "label": "ND",
                                "value": "ND"
                            },
                            {
                                "label": "OH",
                                "value": "OH"
                            },
                            {
                                "label": "OK",
                                "value": "OK"
                            },
                            {
                                "label": "OR",
                                "value": "OR"
                            },
                            {
                                "label": "PA",
                                "value": "PA"
                            },
                            {
                                "label": "RI",
                                "value": "RI"
                            },
                            {
                                "label": "SC",
                                "value": "SC"
                            },
                            {
                                "label": "SD",
                                "value": "SD"
                            },
                            {
                                "label": "TN",
                                "value": "TN"
                            },
                            {
                                "label": "TX",
                                "value": "TX"
                            },
                            {
                                "label": "UT",
                                "value": "UT"
                            },
                            {
                                "label": "VT",
                                "value": "VT"
                            },
                            {
                                "label": "VA",
                                "value": "VA"
                            },
                            {
                                "label": "WA",
                                "value": "WA"
                            },
                            {
                                "label": "WV",
                                "value": "WV"
                            },
                            {
                                "label": "WI",
                                "value": "WI"
                            },
                            {
                                "label": "WY",
                                "value": "WY"
                            }
                        ],
                        "required": {
                            "conditions": [
                                {
                                    "field": "mailingSameAsPrimary",
                                    "value": false,
                                    "comparison": "equals"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    },
                    {
                        "id": "mailzip",
                        "span": "1/2",
                        "type": "text",
                        "label": "Mail Zip",
                        "required": {
                            "conditions": [
                                {
                                    "field": "mailingSameAsPrimary",
                                    "value": false,
                                    "comparison": "equals"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": false
                    }
                ],
                "description": "Home Address"
            }
        ],
        "entityType": "individual",
        "displayName": "Profile"
    },
    "functions": {
        "flagField": {
            "url": "/license/profile/{{context:entityType}}/{{context:entityId}}/reject-fields",
            "body": {
                "fields": [
                    "{{context:fieldId}}"
                ]
            },
            "type": "rest",
            "format": "json",
            "method": "PATCH",
            "description": "Flag field for review"
        },
        "unflagField": {
            "url": "/license/profile/{{context:entityType}}/{{context:entityId}}/reject-fields?field={{context:fieldId}}",
            "type": "rest",
            "format": "json",
            "method": "DELETE",
            "description": "Remove flag from field"
        },
        "saveProfileField": {
            "url": "/license/participant/{{context:entityId}}",
            "body": {
                "{{context:fieldId}}": "{{context:fieldValue}}"
            },
            "type": "rest",
            "format": "formData",
            "method": "PATCH",
            "description": "Save individual field value"
        }
    }
}'::jsonb,  
  CURRENT_TIMESTAMP 
)
ON CONFLICT (properties)
DO UPDATE SET
  json_data = EXCLUDED.json_data,
  last_modified_by = EXCLUDED.last_modified_by,
  last_modified_date = CURRENT_TIMESTAMP;
