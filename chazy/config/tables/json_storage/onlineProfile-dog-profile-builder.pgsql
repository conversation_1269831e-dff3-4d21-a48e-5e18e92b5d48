INSERT INTO config.json_storage (
  json_storage_uuid,
  properties,
  created_by,
  created_date,
  last_modified_by,
  json_data,
  last_modified_date
)
VALUES (
  'ce4684b8-da44-4afb-94ac-f4085cc9e1d7',
  '{"onlineProfile": "dog-profile-builder"}'::jsonb,
  'service-account-clerkxpress-seed', 
  CURRENT_TIMESTAMP, 
  'service-account-clerkxpress-seed',
  '{
    "id": "dog-profile-builder",
    "title": "Profile Configuration",
    "profile": {
        "tab": "Dog",
        "icon": "PawPrint",
        "groups": [
            {
                "order": 1,
                "title": "Basic Information",
                "fields": [
                    {
                        "id": "tagNumber",
                        "span": "1/2",
                        "type": "otp",
                        "label": "Dog Tag Number",
                        "length": 6,
                        "required": true,
                        "validate": [
                            {
                                "type": "minLength",
                                "value": 1,
                                "message": "Tag number must be at least 1 character"
                            },
                            {
                                "type": "maxLength",
                                "value": 6,
                                "message": "Tag number must be 6 characters"
                            }
                        ],
                        "flaggable": true,
                        "editPermissions": [
                            "super-admin"
                        ]
                    },
                    {
                        "id": "microchipNumber",
                        "span": "1/2",
                        "type": "text",
                        "label": "Microchip Number",
                        "required": false,
                        "flaggable": true,
                        "placeholder": "Enter a Microchip Number"
                    },
                    {
                        "id": "dogName",
                        "span": "1/2",
                        "type": "text",
                        "label": "Dog Name",
                        "required": true,
                        "flaggable": true,
                        "placeholder": "Enter a Dog Name"
                    },
                    {
                        "id": "dogBreed",
                        "span": "1/2",
                        "type": "customSelect",
                        "label": "Breed",
                        "options": "{{settings: entity.dog.breeds}}",
                        "required": true,
                        "flaggable": true
                    },
                    {
                        "id": "dogBirthDate",
                        "span": "1/2",
                        "type": "dateofbirth",
                        "label": "Date of Birth",
                        "required": true,
                        "flaggable": true,
                        "placeholder": "Enter a Date of Birth",
                        "formatPattern": "dd-MMMM-yyyy"
                    },
                    {
                        "id": "age",
                        "span": "1/2",
                        "type": "display",
                        "label": "Age",
                        "computed": "calculateAgeInMonthsOrYears",
                        "required": true,
                        "flaggable": false,
                        "computedParams": [
                            "dogBirthDate"
                        ]
                    },
                    {
                        "id": "dogSex",
                        "span": "1/2",
                        "type": "select",
                        "label": "Sex",
                        "options": "{{settings: entity.dog.sex}}",
                        "required": true,
                        "flaggable": false
                    },
                    {
                        "id": "dogSpayedOrNeutered",
                        "if": {
                            "span": "1/2",
                            "type": "boolean",
                            "label": "Spayed",
                            "display": {
                                "yes": "Yes",
                                "exempt": "No"
                            },
                            "options": [
                                {
                                    "label": "Yes",
                                    "value": "yes"
                                },
                                {
                                    "label": "No",
                                    "value": "exempt"
                                }
                            ],
                            "required": true,
                            "flaggable": false
                        },
                        "else": {
                            "span": "1/2",
                            "type": "boolean",
                            "label": "Neutered",
                            "display": {
                                "yes": "Yes",
                                "exempt": "No"
                            },
                            "options": [
                                {
                                    "label": "Yes",
                                    "value": "yes"
                                },
                                {
                                    "label": "No",
                                    "value": "exempt"
                                }
                            ],
                            "required": true,
                            "flaggable": false
                        },
                        "type": "toggleField",
                        "toggleCondition": {
                            "conditions": [
                                {
                                    "field": "dogSex",
                                    "value": "Female",
                                    "comparison": "equals"
                                }
                            ],
                            "conditionType": "AND"
                        }
                    },
                    {
                        "id": "dogPrimaryColor",
                        "span": "1/2",
                        "type": "customSelect",
                        "label": "Primary Color",
                        "options": "{{settings: entity.dog.colors}}",
                        "required": true,
                        "flaggable": false,
                        "placeholder": "Select a Primary Color"
                    },
                    {
                        "id": "dogSecondaryColor",
                        "span": "1/2",
                        "type": "customSelect",
                        "label": "Secondary Color",
                        "options": "{{settings: entity.dog.colors}}",
                        "required": false,
                        "flaggable": false,
                        "placeholder": "Select a Secondary Color"
                    },
                    {
                        "id": "dogBio",
                        "span": "full",
                        "type": "textarea",
                        "label": "Bio",
                        "required": false,
                        "validate": [
                            {
                                "type": "maxLength",
                                "value": 500,
                                "message": "Dog bio must be less than 500 characters"
                            }
                        ],
                        "flaggable": false,
                        "placeholder": "No Bio"
                    },
                    {
                        "id": "dogSpayedOrNeuteredDocument",
                        "span": "1/2",
                        "type": "file",
                        "label": "Spayed or Neutered Document",
                        "accept": [
                            ".pdf",
                            ".jpeg",
                            ".png",
                            ".jpg"
                        ],
                        "visible": {
                            "conditions": [
                                {
                                    "and": {
                                        "field": "isAlteredExempt",
                                        "equals": false
                                    },
                                    "field": "dogSpayedOrNeutered",
                                    "equals": "yes"
                                }
                            ]
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "dogSpayedOrNeutered",
                                    "value": "yes",
                                    "comparison": "equals"
                                },
                                {
                                    "field": "isAlteredExempt",
                                    "value": false,
                                    "comparison": "equals"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    },
                    {
                        "id": "dogSpayedOrNeuteredExemptionDocument",
                        "span": "1/2",
                        "type": "file",
                        "label": "Spayed or Neutered Exemption Document",
                        "accept": [
                            ".pdf",
                            ".jpeg",
                            ".png",
                            ".jpg"
                        ],
                        "visible": {
                            "conditions": [
                                {
                                    "and": {
                                        "field": "isAlteredExempt",
                                        "equals": true
                                    },
                                    "field": "dogSpayedOrNeutered",
                                    "equals": "no"
                                }
                            ]
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "dogSpayedOrNeutered",
                                    "value": "yes",
                                    "unless": {
                                        "permissions": [
                                            "super-admin"
                                        ]
                                    },
                                    "comparison": "equals"
                                },
                                {
                                    "field": "isAlteredExempt",
                                    "value": false,
                                    "comparison": "equals"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    }
                ],
                "description": "Basic information about your dog"
            },
            {
                "order": 2,
                "title": "Rabies Vaccination Information",
                "fields": [
                    {
                        "id": "veterinaryName",
                        "span": "1/2",
                        "type": "text",
                        "label": "Veterinary Name",
                        "required": true,
                        "validate": [
                            {
                                "type": "maxLength",
                                "value": 100,
                                "message": "Veterinary name must be less than 100 characters"
                            }
                        ],
                        "flaggable": true,
                        "placeholder": "Enter a Veterinary Name"
                    },
                    {
                        "id": "vaccineDatesExempt",
                        "span": "1/2",
                        "type": "boolean",
                        "label": "Vaccine Dates Exempt",
                        "options": [
                            {
                                "label": "Yes",
                                "value": "Yes"
                            },
                            {
                                "label": "No",
                                "value": "No"
                            }
                        ],
                        "required": true,
                        "flaggable": true
                    },
                    {
                        "id": "vaccineProducer",
                        "span": "1/2",
                        "type": "select",
                        "label": "Vaccine Producer",
                        "options": "{{settings: entity.dog.rabies_producer}}",
                        "visible": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "equals": false,
                                    "comparison": "coercion"
                                }
                            ]
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    },
                    {
                        "id": "vaccineBrand",
                        "span": "1/2",
                        "type": "select",
                        "label": "Vaccine Brand",
                        "options": "{{settings: entity.dog.rabies_brand}}",
                        "visible": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "equals": false,
                                    "comparison": "coercion"
                                }
                            ]
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    },
                    {
                        "id": "rabiesTagNumber",
                        "span": "1/2",
                        "type": "text",
                        "label": "Rabies Tag Number",
                        "visible": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "equals": false,
                                    "comparison": "coercion"
                                }
                            ]
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true,
                        "placeholder": "Enter a Rabies Tag Number"
                    },
                    {
                        "id": "vaccineAdministeredDate",
                        "span": "1/2",
                        "type": "date",
                        "label": "Vaccine Administered Date",
                        "visible": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "equals": false,
                                    "comparison": "coercion"
                                }
                            ]
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    },
                    {
                        "id": "vaccineDueDate",
                        "span": "1/2",
                        "type": "date",
                        "label": "Vaccine Due Date",
                        "visible": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "equals": false,
                                    "comparison": "coercion"
                                }
                            ]
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    },
                    {
                        "id": "vaccineLotNumber",
                        "span": "1/2",
                        "type": "text",
                        "label": "Lot Number",
                        "visible": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true,
                        "placeholder": "Enter a Lot Number"
                    },
                    {
                        "id": "vaccineLotExpirationDate",
                        "span": "1/2",
                        "type": "date",
                        "label": "Lot Expiration Date",
                        "visible": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ]
                        },
                        "flaggable": true
                    },
                    {
                        "id": "dogRabiesVaccinationDocument",
                        "span": "1/2",
                        "type": "file",
                        "label": "Rabies Document",
                        "accept": [
                            ".pdf",
                            ".jpeg",
                            ".png",
                            ".jpg"
                        ],
                        "visible": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": false,
                                    "comparison": "coercion"
                                }
                            ]
                        },
                        "flaggable": true
                    },
                    {
                        "id": "dogRabiesVaccinationExemptionDocument",
                        "span": "1/2",
                        "type": "file",
                        "label": "Rabies Exemption Document",
                        "accept": [
                            ".pdf",
                            ".jpeg",
                            ".png",
                            ".jpg"
                        ],
                        "visible": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": true,
                                    "comparison": "coercion"
                                }
                            ]
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "vaccineDatesExempt",
                                    "value": true,
                                    "comparison": "coercion"
                                }
                            ],
                            "conditionType": "AND"
                        },
                        "flaggable": true
                    }
                ],
                "description": "Rabies vaccination information"
            },
            {
                "order": 3,
                "title": "Service Dog Information",
                "fields": [
                    {
                        "id": "licenseExempt",
                        "span": "1/2",
                        "type": "boolean",
                        "label": "Service Dog",
                        "options": [
                            {
                                "label": "Yes",
                                "value": "Yes"
                            },
                            {
                                "label": "No",
                                "value": "No"
                            }
                        ],
                        "required": true,
                        "flaggable": true
                    },
                    {
                        "id": "serviceDogType",
                        "span": "1/2",
                        "type": "select",
                        "label": "Service Dog Type",
                        "options": "{{settings: entity.dog.service_dog}}",
                        "visible": {
                            "conditions": [
                                {
                                    "field": "licenseExempt",
                                    "equals": true
                                }
                            ]
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "licenseExempt",
                                    "value": true,
                                    "comparison": "coercion"
                                }
                            ]
                        },
                        "flaggable": true
                    },
                    {
                        "id": "licenseExemptionDocument",
                        "span": "1/2",
                        "type": "file",
                        "label": "Service Dog Verification Document",
                        "accept": [
                            ".pdf",
                            ".jpeg",
                            ".png",
                            ".jpg"
                        ],
                        "visible": {
                            "conditions": [
                                {
                                    "field": "licenseExempt",
                                    "equals": true
                                }
                            ]
                        },
                        "required": {
                            "conditions": [
                                {
                                    "field": "licenseExempt",
                                    "value": true,
                                    "comparison": "coercion"
                                }
                            ]
                        },
                        "flaggable": true
                    }
                ],
                "description": "Service dog certification and details"
            }
        ],
        "entityType": "dog",
        "displayName": "Dog Profile"
    },
    "functions": {
        "flagField": {
            "url": "/license/profile/{{context:entityType}}/{{context:entityId}}/reject-fields",
            "body": {
                "fields": [
                    "{{context:fieldId}}"
                ]
            },
            "type": "rest",
            "format": "json",
            "method": "PATCH",
            "description": "Flag field for review"
        },
        "unflagField": {
            "url": "/license/profile/{{context:entityType}}/{{context:entityId}}/reject-fields?field={{context:fieldId}}",
            "type": "rest",
            "format": "json",
            "method": "DELETE",
            "description": "Remove flag from field"
        },
        "saveProfileField": {
            "url": "/license/participant/{{context:entityId}}",
            "body": {
                "{{context:fieldId}}": "{{context:fieldValue}}"
            },
            "type": "rest",
            "format": "formData",
            "method": "PATCH",
            "description": "Save individual field value"
        }
    }
}'::jsonb,  
  CURRENT_TIMESTAMP 
)
ON CONFLICT (properties)
DO UPDATE SET
  json_data = EXCLUDED.json_data,
  last_modified_by = EXCLUDED.last_modified_by,
  last_modified_date = CURRENT_TIMESTAMP;
