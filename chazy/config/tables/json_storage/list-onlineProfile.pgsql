INSERT INTO config.json_storage (
  json_storage_uuid,
  properties,
  created_by,
  created_date,
  last_modified_by,
  json_data,
  last_modified_date
)
VALUES (
  'ce4684b8-da44-4afb-94ac-f4085cc9e1db',
  '{"list": "onlineProfile"}'::jsonb,
  'service-account-clerkxpress-seed', 
  CURRENT_TIMESTAMP, 
  'service-account-clerkxpress-seed',
  '{
    "forms": {
        "dog-profile-builder": {
            "fields": {
                "title": {
                    "label": "Dog Profile"
                },
                "button": {
                    "href": "/dog-profile",
                    "type": "link",
                    "label": "View Profile"
                }
            },
            "formName": "Dog Profile Builder"
        },
        "individual-profile-builder": {
            "fields": {
                "title": {
                    "label": "Individual Profile"
                },
                "button": {
                    "href": "/individual-profile",
                    "type": "link",
                    "label": "View Profile"
                }
            },
            "formName": "Individual Profile Builder"
        }
    },
    "formList": [
        {
            "forms": [
                {
                    "form": "dog-profile-builder"
                }
            ],
            "category": "Dog Profiles"
        },
        {
            "forms": [
                {
                    "form": "individual-profile-builder"
                }
            ],
            "category": "Individual Profiles"
        }
    ]
}'::jsonb,  
  CURRENT_TIMESTAMP 
)
ON CONFLICT (properties)
DO UPDATE SET
  json_data = EXCLUDED.json_data,
  last_modified_by = EXCLUDED.last_modified_by,
  last_modified_date = CURRENT_TIMESTAMP;
