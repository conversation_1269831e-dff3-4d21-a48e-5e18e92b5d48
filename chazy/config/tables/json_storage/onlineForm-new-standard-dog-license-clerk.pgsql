UPDATE config.json_storage
SET json_data = $$
{
    "id": "new-standard-dog-license-clerk",
    "pages": {
        "error": {
            "title": "Error Loading Page",
            "settings": [
                "hideSidebar"
            ],
            "paragraph": "An error occurred while submitting your registration. Please try again later.",
            "navigation": [
                {
                    "type": "button",
                    "label": "Try Again",
                    "action": {
                        "goToPage": "instructions"
                    },
                    "variant": "primary"
                },
                {
                    "type": "link",
                    "label": "Cancel",
                    "variant": "ghost",
                    "navigate": {
                        "url": "/home"
                    }
                }
            ]
        },
        "editDog": {
            "title": "Let's get started.",
            "fields": [
                {
                    "id": "dog.tagNumber",
                    "size": 6,
                    "type": "code",
                    "label": "Tag Number",
                    "required": true,
                    "validate": [
                        {
                            "type": "charMaxLength",
                            "value": 6,
                            "message": "Tag number must not exceed 6 characters."
                        },
                        {
                            "type": "charMinLength",
                            "value": 4,
                            "message": "Tag number must be a minimum of 4 characters."
                        }
                    ]
                },
                {
                    "id": "dog.dogName",
                    "type": "text",
                    "label": "Dog Name",
                    "required": true,
                    "validate": [
                        {
                            "type": "charMaxLength",
                            "value": 50,
                            "message": "Dog name must be less than 50 characters."
                        }
                    ]
                },
                {
                    "id": "dog.dogBreed",
                    "type": "select",
                    "label": "Dog Breed",
                    "options": "{{settings:entity.dog.breeds}}",
                    "required": true
                },
                {
                    "id": "dog.dogSex",
                    "type": "select",
                    "label": "Dog Sex",
                    "options": "{{settings:entity.dog.sex}}",
                    "required": true
                },
                {
                    "id": "dog.dogSpayedOrNeutered",
                    "type": "select",
                    "label": "Spayed or Neutered?",
                    "options": [
                        {
                            "label": "Yes",
                            "value": "yes"
                        },
                        {
                            "label": "No",
                            "value": "no"
                        }
                    ],
                    "required": {
                        "conditions": [
                            {
                                "field": "dog.spayNeuterExemption",
                                "value": false
                            }
                        ]
                    },
                    "className": "",
                    "placeholder": "Select options",
                    "displayConditions": {
                        "conditions": [
                            {
                                "field": "dog.spayNeuterExemption",
                                "value": false
                            }
                        ]
                    }
                },
                {
                    "id": "dog.dogBirthDate",
                    "type": "date",
                    "label": "Dog Birth Date",
                    "required": true,
                    "validate": [
                        {
                            "type": "datePastMin",
                            "unit": "years",
                            "value": 0,
                            "message": "Dog birth date must be in the past."
                        },
                        {
                            "type": "datePastMax",
                            "unit": "years",
                            "value": 32,
                            "message": "Dog birth must be 32 years ago or less."
                        }
                    ]
                },
                {
                    "id": "dog.microchipNumber",
                    "type": "text",
                    "label": "Microchip Number",
                    "required": false,
                    "validate": [
                        {
                            "type": "charMaxLength",
                            "value": 16,
                            "message": "Microchip number must be less than 16 characters."
                        }
                    ]
                },
                {
                    "id": "dog.dogPrimaryColor",
                    "type": "select",
                    "label": "Primary Color",
                    "options": "{{settings:entity.dog.colors}}",
                    "required": true,
                    "placeholder": "Select Primary Color"
                },
                {
                    "id": "dog.dogSecondaryColor",
                    "type": "select",
                    "label": "Secondary Color",
                    "options": "{{settings:entity.dog.colors}}",
                    "required": false,
                    "placeholder": "Select Secondary Color"
                }
            ],
            "paragraph": "Please enter your information below:",
            "navigation": [
                {
                    "type": "button",
                    "label": "Next",
                    "action": {
                        "goToPage": "dogProfile"
                    },
                    "variant": "primary"
                },
                {
                    "type": "button",
                    "label": "Back",
                    "action": {
                        "goToPage": "exemptions"
                    },
                    "variant": "ghost",
                    "operation": "back"
                }
            ]
        },
        "success": {
            "title": "Success",
            "settings": [
                "hideSidebar"
            ],
            "paragraph": "You have successfully applied for a new License. Please navigate to the cart page to continue.",
            "navigation": [
                {
                    "type": "link",
                    "label": "Go to Cart",
                    "variant": "primary",
                    "navigate": {
                        "url": "/cart"
                    }
                }
            ]
        },
        "dogProfile": {
            "title": "Dog Profile Information (Optional)",
            "fields": [
                {
                    "id": "dog.avatar",
                    "type": "avatar",
                    "label": "Dog Profile Picture",
                    "required": false,
                    "avatarType": "dog",
                    "acceptedFileType": [
                        "jpeg",
                        "jpg",
                        "png"
                    ]
                },
                {
                    "id": "dog.dogBio",
                    "type": "textarea",
                    "label": "Dog Bio",
                    "required": false,
                    "validate": [
                        {
                            "type": "charMaxLength",
                            "value": 500,
                            "message": "Dog bio must be less than 500 characters."
                        }
                    ]
                }
            ],
            "paragraph": "This information is used to help in the recovery of your dog, in the event they are lost, and to help others identify your dog. This information is optional.",
            "navigation": [
                {
                    "type": "button",
                    "label": "Next",
                    "action": {
                        "guard": {
                            "field": "dog.vaccineDatesExempt",
                            "value": true,
                            "matchType": "boolean"
                        },
                        "onError": {
                            "goToPage": "vaccineInformation"
                        },
                        "onSuccess": {
                            "goToPage": "confirmationDog"
                        }
                    },
                    "variant": "primary"
                },
                {
                    "type": "button",
                    "label": "Back",
                    "action": {
                        "goToPage": "editDog"
                    },
                    "variant": "ghost",
                    "operation": "back"
                }
            ]
        },
        "exemptions": {
            "title": "Dog License Exemptions",
            "fields": [
                {
                    "id": "dog.spayNeuterExemption",
                    "type": "checkbox",
                    "label": "Dog is exempt from spay/neuter requirement"
                },
                {
                    "id": "dog.vaccineDatesExempt",
                    "type": "checkbox",
                    "label": "Dog is exempt from rabies vaccinations"
                },
                {
                    "id": "dog.licenseExempt",
                    "type": "checkbox",
                    "label": "Additional Exemptions",
                    "checked": true,
                    "information": "Types of service animals include: Guide Dog, Hearing Dog, Service Dog, Working Search Dog, War Dog, Detection Dog, Police Work Dog, or Therapy Dog."
                },
                {
                    "id": "dog.serviceDogType",
                    "type": "select",
                    "label": "Service Dog Type",
                    "options": "{{settings:entity.dog.service_dog}}",
                    "required": {
                        "conditions": [
                            {
                                "field": "dog.licenseExempt",
                                "value": true
                            }
                        ]
                    },
                    "displayConditions": {
                        "conditions": [
                            {
                                "field": "dog.licenseExempt",
                                "value": true
                            }
                        ]
                    }
                }
            ],
            "paragraph": "Please select all applicable exemptions.",
            "navigation": [
                {
                    "type": "button",
                    "label": "Next",
                    "action": {
                        "goToPage": "editDog"
                    },
                    "variant": "primary",
                    "operation": "submit"
                },
                {
                    "type": "button",
                    "label": "Cancel",
                    "action": {
                        "goToLink": "{{context:returnTo}}"
                    },
                    "variant": "ghost"
                }
            ]
        },
        "confirmationDog": {
            "title": "Confirm Dog Information",
            "fields": [
                {
                    "id": "basicInfo",
                    "type": "confirmationGroup",
                    "label": "Basic Dog Info",
                    "value": ""
                },
                {
                    "id": "dog.tagNumber",
                    "size": 6,
                    "type": "code",
                    "label": "Tag Number",
                    "groupId": "basicInfo",
                    "disabled": true,
                    "required": true
                },
                {
                    "id": "dog.dogName",
                    "type": "text",
                    "label": "Dog Name",
                    "groupId": "basicInfo",
                    "disabled": true,
                    "required": true
                },
                {
                    "id": "dog.dogBreed",
                    "type": "select",
                    "label": "Dog Breed",
                    "groupId": "basicInfo",
                    "options": "{{settings:entity.dog.breeds}}",
                    "disabled": true,
                    "required": true
                },
                {
                    "id": "dog.dogSex",
                    "type": "select",
                    "label": "Dog Sex",
                    "groupId": "basicInfo",
                    "options": "{{settings:entity.dog.sex}}",
                    "disabled": true,
                    "required": true
                },
                {
                    "id": "dog.dogBirthDate",
                    "type": "date",
                    "label": "Dog Birth Date",
                    "groupId": "basicInfo",
                    "disabled": true,
                    "required": true
                },
                {
                    "id": "dog.dogPrimaryColor",
                    "type": "select",
                    "label": "Primary Color",
                    "groupId": "basicInfo",
                    "options": "{{settings:entity.dog.colors}}",
                    "disabled": true,
                    "required": true
                },
                {
                    "id": "vaccineInfo",
                    "type": "confirmationGroup",
                    "label": "Dog Vaccine Info",
                    "value": ""
                },
                {
                    "id": "dog.vaccineName",
                    "type": "select",
                    "label": "Vaccine Name",
                    "groupId": "vaccineInfo",
                    "options": [
                        {
                            "label": "Rabies",
                            "value": "Rabies"
                        }
                    ],
                    "disabled": true,
                    "required": true,
                    "displayConditions": {
                        "conditions": [
                            {
                                "field": "dog.vaccineDatesExempt",
                                "value": false
                            }
                        ]
                    }
                },
                {
                    "id": "dog.vaccineProducer",
                    "type": "select",
                    "label": "Vaccine Producer",
                    "groupId": "vaccineInfo",
                    "options": "{{settings:entity.dog.rabies_producer}}",
                    "disabled": true,
                    "required": true,
                    "displayConditions": {
                        "conditions": [
                            {
                                "field": "dog.vaccineDatesExempt",
                                "value": false
                            }
                        ]
                    }
                },
                {
                    "id": "dog.vaccineBrand",
                    "type": "select",
                    "label": "Vaccine Brand",
                    "groupId": "vaccineInfo",
                    "options": "{{settings:entity.dog.rabies_brand}}",
                    "disabled": true,
                    "required": true,
                    "displayConditions": {
                        "conditions": [
                            {
                                "field": "dog.vaccineDatesExempt",
                                "value": false
                            }
                        ]
                    }
                },
                {
                    "id": "dog.vaccineAdministeredDate",
                    "type": "date",
                    "label": "Vaccine Administered Date",
                    "groupId": "vaccineInfo",
                    "disabled": true,
                    "required": true,
                    "displayConditions": {
                        "conditions": [
                            {
                                "field": "dog.vaccineDatesExempt",
                                "value": false
                            }
                        ]
                    }
                },
                {
                    "id": "dog.vaccineDueDate",
                    "type": "date",
                    "label": "Vaccine Due Date",
                    "groupId": "vaccineInfo",
                    "disabled": true,
                    "required": true,
                    "displayConditions": {
                        "conditions": [
                            {
                                "field": "dog.vaccineDatesExempt",
                                "value": false
                            }
                        ]
                    }
                },
                {
                    "id": "dog.vaccineDatesExempt",
                    "type": "checkbox",
                    "label": "Rabies Exemption",
                    "groupId": "vaccineInfo",
                    "disabled": true,
                    "required": false,
                    "displayConditions": {
                        "conditions": [
                            {
                                "field": "dog.vaccineDatesExempt",
                                "value": true
                            }
                        ]
                    }
                }
            ],
            "settings": [
                "hideSidebar"
            ],
            "paragraph": "Please confirm the following information is correct:",
            "navigation": [
                {
                    "type": "button",
                    "label": "Confirm",
                    "action": {
                        "guard": {
                            "field": "license.licenseId",
                            "value": null,
                            "matchType": "isNull"
                        },
                        "onError": {
                            "callApi": "getLicenseFees",
                            "onError": {
                                "goToPage": "error"
                            },
                            "onSuccess": {
                                "goToPage": "licenseDurationInformation",
                                "assignEvent": {
                                    "license.pricing": "{{event:items}}",
                                    "license.maxLicenseDuration": "{{event:duration}}"
                                }
                            }
                        },
                        "onSuccess": {
                            "callApi": "createDraftLicense",
                            "onError": {
                                "goToPage": "confirmationDog"
                            },
                            "onSuccess": {
                                "callApi": "createDog",
                                "onError": {
                                    "goToPage": "confirmationDog"
                                },
                                "onSuccess": {
                                    "callApi": "updateTag",
                                    "onError": {
                                        "goToPage": "error"
                                    },
                                    "onSuccess": {
                                        "callApi": "getLicenseFees",
                                        "onError": {
                                            "goToPage": "error"
                                        },
                                        "onSuccess": {
                                            "goToPage": "licenseDurationInformation",
                                            "assignEvent": {
                                                "license.pricing": "{{event:items}}",
                                                "license.maxLicenseDuration": "{{event:duration}}"
                                            }
                                        }
                                    },
                                    "assignEvent": {
                                        "dog.tenant": "{{event:tenant}}",
                                        "dog.dogEntityId": "{{event:entityId}}",
                                        "dog.dogEntityType": "{{event:entityType}}"
                                    }
                                },
                                "assignEvent": {
                                    "license.licenseId": "{{event:entityId}}"
                                }
                            }
                        }
                    },
                    "variant": "primary"
                },
                {
                    "type": "button",
                    "label": "Back",
                    "action": {
                        "guard": {
                            "field": "dog.vaccineDatesExempt",
                            "value": true,
                            "matchType": "boolean"
                        },
                        "onError": {
                            "guard": {
                                "field": "dog.vaccineDatesExempt",
                                "value": false,
                                "matchType": "boolean"
                            },
                            "onError": {
                                "goToPage": "vaccineInformation"
                            },
                            "onSuccess": {
                                "goToPage": "vaccineInformation"
                            }
                        },
                        "onSuccess": {
                            "goToPage": "dogProfile"
                        }
                    },
                    "variant": "ghost",
                    "operation": "back"
                }
            ]
        },
        "vaccineInformation": {
            "title": "Vaccine Information",
            "fields": [
                {
                    "id": "dog.veterinaryName",
                    "type": "text",
                    "label": "Veterinary Name",
                    "required": true
                },
                {
                    "id": "dog.vaccineName",
                    "type": "select",
                    "label": "Vaccine Name",
                    "options": [
                        {
                            "label": "Rabies",
                            "value": "Rabies"
                        }
                    ],
                    "required": true
                },
                {
                    "id": "dog.vaccineProducer",
                    "type": "select",
                    "label": "Vaccine Producer",
                    "options": "{{settings:entity.dog.rabies_producer}}",
                    "required": true
                },
                {
                    "id": "dog.vaccineBrand",
                    "type": "select",
                    "label": "Vaccine Brand",
                    "options": "{{settings:entity.dog.rabies_brand}}",
                    "required": true
                },
                {
                    "id": "dog.rabiesTagNumber",
                    "type": "text",
                    "label": "Rabies Tag Number",
                    "required": false
                },
                {
                    "id": "dog.vaccineAdministeredDate",
                    "type": "date",
                    "label": "Vaccine Administered Date",
                    "required": true,
                    "validate": [
                        {
                            "type": "futureMax",
                            "unit": "months",
                            "value": 0,
                            "message": "Vaccine administered date must be in the past."
                        }
                    ]
                },
                {
                    "id": "dog.vaccineDueDate",
                    "type": "date",
                    "label": "Vaccine Due Date",
                    "required": true,
                    "validate": [
                        {
                            "type": "futureMin",
                            "unit": "months",
                            "value": 1,
                            "message": "Vaccine due date must have at least 1 month of validity."
                        }
                    ]
                },
                {
                    "id": "dog.vaccineLotNumber",
                    "type": "text",
                    "label": "Vaccine Lot Number",
                    "required": false
                },
                {
                    "id": "dog.vaccineLotExpirationDate",
                    "type": "date",
                    "label": "Vaccine Lot Expiration Date",
                    "required": false
                }
            ],
            "paragraph": "Please enter the vaccine information for your dog:",
            "navigation": [
                {
                    "type": "button",
                    "label": "Next",
                    "action": {
                        "goToPage": "confirmationDog"
                    },
                    "variant": "primary"
                },
                {
                    "type": "button",
                    "label": "Back",
                    "action": {
                        "goToPage": "dogProfile"
                    },
                    "variant": "ghost",
                    "operation": "back"
                }
            ]
        },
        "licenseDurationInformation": {
            "title": "How many years would you like to register for?",
            "fields": [
                {
                    "id": "license.pricing",
                    "data": "{{context:license.pricing}}",
                    "type": "table",
                    "label": "License Pricing",
                    "sortBy": {
                        "order": "asc",
                        "column": "total"
                    },
                    "columns": [
                        {
                            "id": "label",
                            "type": "text",
                            "label": "License Duration"
                        },
                        {
                            "id": "total",
                            "type": "currency",
                            "label": "Price",
                            "currencySymbol": "$"
                        }
                    ],
                    "required": false,
                    "paragraph": "Below are the prices for each license duration:",
                    "allowSearch": false,
                    "allowSorting": false,
                    "allowPagination": false
                },
                {
                    "id": "license.licenseDuration",
                    "type": "select",
                    "label": "License Duration",
                    "sortBy": {
                        "order": "asc",
                        "column": "total"
                    },
                    "options": "{{context:license.pricing}}",
                    "required": true,
                    "placeholder": "Select a Duration",
                    "optionValueMap": {
                        "label": "label",
                        "value": "duration"
                    }
                }
            ],
            "paragraph": "Please enter the duration of the license:",
            "navigation": [
                {
                    "type": "button",
                    "label": "Next",
                    "action": {
                        "goToPage": "confirmationLicenseDuration"
                    },
                    "variant": "primary"
                }
            ]
        },
        "confirmationLicenseDuration": {
            "title": "Confirm Selections",
            "fields": [
                {
                    "id": "licenseDuration",
                    "type": "confirmationGroup",
                    "label": "License Duration",
                    "value": ""
                },
                {
                    "id": "license.licenseDuration",
                    "type": "select",
                    "label": "License Duration",
                    "value": "{license.licenseDuration}",
                    "sortBy": {
                        "order": "asc",
                        "column": "total"
                    },
                    "groupId": "licenseDuration",
                    "options": "{{context:license.pricing}}",
                    "disabled": true,
                    "required": true,
                    "optionValueMap": {
                        "label": "label",
                        "value": "duration"
                    }
                }
            ],
            "settings": [
                "hideSidebar"
            ],
            "paragraph": "Please confirm the following information is correct:",
            "navigation": [
                {
                    "type": "button",
                    "label": "Confirm",
                    "action": {
                        "callApi": "sendDuration",
                        "onError": {
                            "goToPage": "confirmationLicenseDuration"
                        },
                        "onSuccess": {
                            "callApi": "addToCart",
                            "onError": {
                                "goToPage": "success"
                            },
                            "onSuccess": {
                                "goToPage": "success"
                            }
                        }
                    },
                    "variant": "primary"
                },
                {
                    "type": "button",
                    "label": "Back",
                    "action": {
                        "goToPage": "licenseDurationInformation"
                    },
                    "variant": "ghost",
                    "operation": "back"
                }
            ]
        }
    },
    "context": {
        "dog": {
            "avatar": null,
            "dogBio": null,
            "dogSex": null,
            "dogTag": null,
            "tenant": null,
            "dogName": null,
            "dogBreed": null,
            "tagNumber": null,
            "catFriendly": null,
            "dogEntityId": null,
            "dogFriendly": null,
            "dogMarkings": null,
            "vaccineName": "Rabies",
            "dogBirthDate": null,
            "vaccineBrand": null,
            "childFriendly": null,
            "dogEntityType": null,
            "licenseExempt": false,
            "serviceDogType": null,
            "vaccineDueDate": null,
            "veterinaryName": null,
            "dogPrimaryColor": null,
            "microchipNumber": null,
            "rabiesTagNumber": null,
            "vaccineProducer": null,
            "vaccineLotNumber": null,
            "dogSecondaryColor": null,
            "vaccineDatesExempt": false,
            "dogSpayedOrNeutered": null,
            "spayNeuterExemption": false,
            "vaccineAdministeredDate": null,
            "vaccineLotExpirationDate": null
        },
        "error": null,
        "license": {
            "pricing": null,
            "licenseId": null,
            "licenseType": "dogLicense",
            "licenseDuration": null,
            "maxLicenseDuration": null
        },
        "cartItemType": "license",
        "individualId": null
    },
    "sidebar": [
        {
            "label": "Exemptions",
            "order": 1,
            "pages": [
                "exemptions"
            ]
        },
        {
            "label": "Dog Information",
            "order": 2,
            "pages": [
                "editDog",
                "dogProfile",
                "vaccineInformation"
            ]
        },
        {
            "label": "License Duration",
            "order": 3,
            "pages": [
                "licenseDurationInformation"
            ]
        },
        {
            "label": "Confirmation",
            "order": 4,
            "pages": [
                "confirmationDog",
                "confirmationLicenseDuration"
            ]
        }
    ],
    "formName": "New Dog License Form",
    "functions": {
        "addToCart": {
            "type": "function",
            "function": "addToCart",
            "parameters": {
                "entityId": "{{context:license.licenseId}}",
                "entityType": "license"
            },
            "description": "Add the license to the cart"
        },
        "createDog": {
            "url": "/license/license/{{context:license.licenseId}}/add-dog",
            "body": {
                "avatar": "{{context:dog.avatar}}",
                "dogBio": "{{context:dog.dogBio}}",
                "dogSex": "{{context:dog.dogSex}}",
                "dogName": "{{context:dog.dogName}}",
                "dogBreed": "{{context:dog.dogBreed}}",
                "tagNumber": "{{context:dog.tagNumber}}",
                "catFriendly": "{{context:dog.catFriendly}}",
                "dogFriendly": "{{context:dog.dogFriendly}}",
                "dogMarkings": "{{context:dog.dogMarkings}}",
                "vaccineName": "{{context:dog.vaccineName}}",
                "dogBirthDate": "{{context:dog.dogBirthDate}}",
                "vaccineBrand": "{{context:dog.vaccineBrand}}",
                "childFriendly": "{{context:dog.childFriendly}}",
                "licenseExempt": "{{context:dog.licenseExempt}}",
                "serviceDogType": "{{context:dog.serviceDogType}}",
                "vaccineDueDate": "{{context:dog.vaccineDueDate}}",
                "veterinaryName": "{{context:dog.veterinaryName}}",
                "dogPrimaryColor": "{{context:dog.dogPrimaryColor}}",
                "microchipNumber": "{{context:dog.microchipNumber}}",
                "rabiesTagNumber": "{{context:dog.rabiesTagNumber}}",
                "vaccineProducer": "{{context:dog.vaccineProducer}}",
                "vaccineLotNumber": "{{context:dog.vaccineLotNumber}}",
                "dogSecondaryColor": "{{context:dog.dogSecondaryColor}}",
                "vaccineDatesExempt": "{{context:dog.vaccineDatesExempt}}",
                "dogSpayedOrNeutered": "{{context:dog.dogSpayedOrNeutered}}",
                "spayNeuterExemption": "{{context:dog.spayNeuterExemption}}",
                "vaccineAdministeredDate": "{{context:dog.vaccineAdministeredDate}}",
                "vaccineLotExpirationDate": "{{context:dog.vaccineLotExpirationDate}}"
            },
            "type": "rest",
            "format": "formData",
            "method": "POST",
            "description": "Create a new dog"
        },
        "updateTag": {
            "url": "/license/code-lookup/tag-update",
            "body": {
                "tenant": "{{context:dog.tenant}}",
                "entityId": "{{context:dog.dogEntityId}}",
                "tagNumber": "{{context:dog.tagNumber}}"
            },
            "type": "rest",
            "format": "json",
            "method": "PATCH",
            "description": "Update dog tag"
        },
        "sendDuration": {
            "url": "/license/license/final/{{context:license.licenseId}}?duration={{context:license.licenseDuration}}",
            "type": "rest",
            "format": "json",
            "method": "POST",
            "description": "Send the duration of the license"
        },
        "getLicenseFees": {
            "url": "/license/license/{{context:license.licenseId}}/fees/calculate",
            "type": "rest",
            "format": "json",
            "method": "GET",
            "description": "Get the fees for the license"
        },
        "createDraftLicense": {
            "url": "/license/license/pending",
            "body": {
                "licenseType": "{{context:license.licenseType}}",
                "participantId": "{{context:individualId}}"
            },
            "type": "rest",
            "format": "json",
            "method": "POST",
            "description": "Create a new draft license"
        }
    },
    "initialPage": "exemptions",
    "permissions": [
        "super-admin"
    ],
    "requirements": [
        "individualId"
    ]
}
$$::jsonb, 
last_modified_date = CURRENT_TIMESTAMP
WHERE properties = '{"onlineForm": "new-standard-dog-license-clerk"}'::jsonb;
