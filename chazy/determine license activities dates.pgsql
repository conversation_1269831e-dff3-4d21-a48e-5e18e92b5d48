--drop function determineLicenseActivityDate;
create or replace function license.determineLicenseActivityDate(
    IN v_license_uuid uuid
)
RETURNS TABLE(
    valid_from_date TIMESTAMP,
    valid_to_date TIMESTAMP
)
LANGUAGE plpgsql
AS $$
declare
	v_license record;
    v_latest_activity record;
	r_valid_to_date TIMESTAMP;
	r_valid_from_date TIMESTAMP;

	month_num INTEGER;
begin
	-- lookup the license
	select 
		l.*
	into v_license
	from license.license l
	where l.license_uuid = v_license_uuid;

    if v_license is null then
        raise exception 'License with UUID % not found', v_license_uuid;
    end if;

	-- get the latest activity
	select
		la.*
	into v_latest_activity
	from license.license_activity la 
	where la.license_id = v_license.license_id
	and la.valid_to_date is not null
	order by la.valid_to_date desc
	limit 1;

	month_num:= 
		CASE EXTRACT(MONTH FROM current_date)
	        WHEN 1 THEN  17
	        WHEN 2 THEN  16
	        WHEN 3 THEN  15
	        WHEN 4 THEN  14
	        WHEN 5 THEN  13
	        WHEN 6 THEN  12
	        WHEN 7 THEN  11
	        WHEN 8 THEN  10
	        WHEN 9 THEN  9
	        WHEN 10 THEN  8
	        WHEN 11 THEN  7
	        WHEN 12 THEN  6
	    END CASE;

	if v_latest_activity is null or v_latest_activity.license_activity_id is null then
		if v_license.valid_to_date is null then
			r_valid_from_date:= license.start_of_day(now());
		ELSE
			r_valid_from_date:=  license.start_of_day(v_license.valid_to_date);
		end if;
	ELSE
		r_valid_from_date:=  license.start_of_day(v_latest_activity.valid_to_date);
	end if;

	--check if the r_valid_from_date is in the past we must bring up to date
	if r_valid_from_date < license.start_of_day(now()) then
		r_valid_from_date:= license.start_of_day(now());
	end if;

	--if r_valid_from_date <= today then
	if r_valid_from_date::date = now()::date then
		r_valid_to_date:= license.end_of_day(concat('06/30/', EXTRACT(YEAR FROM (r_valid_from_date + interval'1 year' * month_num/12)))::TIMESTAMPTZ);
	else
		r_valid_to_date:= license.end_of_day(concat('06/30/', EXTRACT(YEAR FROM r_valid_from_date) + 1)::TIMESTAMPTZ);
	end if;

	return query
	select
		r_valid_from_date as valid_from_date,
		r_valid_to_date as valid_to_date;
END;
$$;

--existing
--select * from determineLicenseActivityDate('1c05a167-423b-45de-a9c2-7489185b049b');

--new
--select * from determineLicenseActivityDate('d5d5623e-0742-4eae-a752-7ca29f1aefc7');