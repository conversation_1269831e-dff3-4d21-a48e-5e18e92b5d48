select * from audit_log_license
where license_uuid = '66e35cec-7264-41be-ad30-3d0715ced884'
order by revision_id desc

select * from license_status

select * from license.generate_insert_script('license', 'audit_log_license','license_uuid = ''66e35cec-7264-41be-ad30-3d0715ced884''','{audit_log_license_id}')

select 
	*
from license l
where not exists(
	select * from audit_log_license i
	where i.license_id = l.license_id
)

INSERT INTO license.audit_log_revision("timestamp", auditor)
VALUES (EXTRACT(EPOCH FROM now())::bigint, 'conversion')
RETURNING audit_log_revision_id;




INSERT INTO license.audit_log_license (license_id, revision_id, revision_type, conversion_reference, last_modified_by, last_modified_date, application_date, issued_date, license_number, valid_from_date, valid_to_date, license_status_id, license_type_id, modifier, events, approved, approved_date, denied_comment, association_type, properties, license_uuid, approved_by, dummy_column) 
VALUES ('9638', '5340088', '0', '3105', '<EMAIL>', '2025-05-13T12:32:45.944705+00:00', '2021-03-25T00:00:00+00:00', '2025-05-13T12:25:30.784144+00:00', '11519', '2021-03-25T00:00:00+00:00', '2025-06-30T00:00:00+00:00', '2', '1', NULL, '[{"uuid": "78b1c02f-11e8-4f53-8f4b-53ffa1d28b0a", "action": "license approved", "comment": null, "metadata": null, "createdBy": "<EMAIL>", "inherited": false, "createdDate": "2025-05-13T12:25:30.784147857Z", "eventTypeId": 26}]', 'true', '2025-05-13T12:25:30.784069', NULL, NULL, '{"statusDate": "2024-03-27T00:00:00", "actionNotes": null, "actionTaken": 0, "licenseForm": "3ebd10f4-3c05-486e-bd3d-fbaf33abeddc", "licenseLabel": "1 year", "licenseExempt": "false", "rejectedFields": [], "licenseDuration": 1}', '66e35cec-7264-41be-ad30-3d0715ced884', '<EMAIL>', 'LICENSE');

