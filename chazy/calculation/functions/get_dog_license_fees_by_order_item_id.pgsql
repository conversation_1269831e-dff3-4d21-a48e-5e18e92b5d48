--drop function get_dog_license_fees_by_order_item_id;
create or replace function calculation.get_dog_license_fees_by_order_item_id(
    IN v_order_item_id bigint
)
RETURNS TABLE(
    state_fees numeric, 
    local_fees numeric, 
    total_fees numeric, 
    is_exempt boolean, 
    has_senior_discount boolean, 
    is_altered boolean
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    WITH cte AS (
        SELECT
            SUM(CASE WHEN f.key LIKE '%-S-%' THEN oif.price ELSE 0 END) AS state_fees,
            SUM(CASE WHEN f.key NOT LIKE '%-S-%' THEN oif.price ELSE 0 END) AS local_fees,
            MAX(CASE WHEN f.key = 'DL-M-EXEMPT' THEN 1 ELSE 0 END) AS is_exempt,
            MAX(CASE WHEN f.key = 'DL-M-SENIOR' THEN 1 ELSE 0 END) AS has_senior_discount,
            MAX(CASE WHEN f.key LIKE '%-S-ALT' THEN 1 ELSE 0 END) AS is_altered
        FROM calculation.order_item_fee oif
        INNER JOIN calculation.fee f
            ON f.fee_id = oif.fee_id
        WHERE oif.order_item_id = v_order_item_id
    )
    SELECT
        COALESCE(cte.state_fees, 0.00),
        COALESCE(cte.local_fees, 0.00),
        COALESCE(cte.state_fees + cte.local_fees, 0.00) AS total_fees,
        (cte.is_exempt = 1) AS is_exempt,
        (cte.has_senior_discount = 1) AS has_senior_discount,
        (cte.is_altered = 1) AS is_altered
    FROM cte;
END;
$$;