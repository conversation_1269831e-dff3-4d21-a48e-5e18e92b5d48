create or replace function license.fn_dogLicenseFeeCalculation(
	in i_license_entity_id uuid
)
returns table (
	fee_type text,
	fee_code text,
	fee_amount NUMERIC(20, 10)
)
LANGUAGE plpgsql
AS $$
BEGIN
	RETURN QUERY
	SELECT
	*
	FROM license.fn_dogLicenseFeeCalculation(i_license_entity_id, false, true) f;
END;
$$;


--drop function if exists fn_dogLicenseFeeCalculation(uuid);
create or replace function license.fn_dogLicenseFeeCalculation(
	in i_license_entity_id uuid,
	in i_is_renewal boolean,
	in i_is_late boolean
)
returns table (
	fee_type text,
	fee_code text,
	fee_amount NUMERIC(20, 10)
)
LANGUAGE plpgsql
AS $$
declare
	v_license record;
	v_latest_license_activity record;
	v_dog_properties jsonb;
	v_owner_properties jsonb;
	v_duration_months int := (18 - EXTRACT(MONTH FROM now()));
	v_spayed_neutered_rate numeric := 0.75;
	v_unspayed_unneutered_rate numeric := 1.42;
	v_late_fee_rate numeric := 5.00;
	v_late_capped_amount numeric := 25.00;
	v_renewal_spayed_neutered_cost numeric := 9.00;
	v_renewal_unspayed_unneutered_cost numeric := 17.00;

	v_is_spayed_or_neutered boolean := false;
	v_is_senior boolean := false;
	v_is_exempt boolean := false;

	v_is_renewal boolean := false;
	v_late_month_count int := 0;
	v_late_days_count int := 0;
BEGIN
    select 
    	l.*
	into v_license
	from license.view_license l
	where l.license_type_code = 'dogLicense'
	and l.license_uuid = i_license_entity_id;

	if v_license is null then
        raise exception 'License with UUID % not found', i_license_entity_id;
    end if;

	select 
		la.*
	into v_latest_license_activity
	from license.license_activity la
	where la.license_id = v_license.license_id
	order by la.license_activity_id desc
	limit 1;

	if i_is_renewal then
		v_is_renewal := TRUE;
	else
		-- if there is an activity then it is a renewal
		-- if no activity yet then it'll be new license
		v_is_renewal := v_latest_license_activity.activity_type is not null; 
	end if;

	SELECT
		pdog.properties
	INTO v_dog_properties	
	FROM license.association aDog
	LEFT JOIN license.view_participant pdog
		on pdog.participant_id = aDog.child_id
    WHERE aDog.parent_association_type = 'LICENSE'
    AND aDog.child_association_type = 'PARTICIPANT'
    AND aDog.parent_id = v_license.license_id
	and pdog.group_name ILIKE 'Dog';

	if v_dog_properties is null then
		raise exception 'Dog not found for license with UUID %', i_license_entity_id;
	end if;

	select 
		powner.properties
	into v_owner_properties
	from license.association aOwner
	LEFT JOIN license.view_participant powner
		on powner.participant_id = aOwner.child_id
	where aOwner.parent_association_type = 'LICENSE'
	and aOwner.child_association_type = 'PARTICIPANT'
	and aOwner.parent_id = v_license.license_id
	and powner.group_name ILIKE 'Individual';

	if v_owner_properties is null then
		raise exception 'Owner not found for license with UUID %', i_license_entity_id;
	end if;

	if COALESCE(v_dog_properties->>'dogSpayedOrNeutered','no') ILIKE 'yes' then
		v_is_spayed_or_neutered := true;
	end if;

	if (v_owner_properties->>'dateOfBirth')::date <= (now() - interval '65 years')::date then
		v_is_senior := true;
	end if;

	if COALESCE(v_dog_properties->>'licenseExempt','false') ILIKE 'true' then
		v_is_exempt := true;
	end if;

		-- late fees is only applicable for renewals
	-- compare today's date with the license_activity valid_to_date and compare how many months late the renewal is
	if v_is_renewal and i_is_late and COALESCE(license.start_of_day(v_latest_license_activity.valid_to_date), CURRENT_DATE) <= CURRENT_DATE then
		v_late_month_count := license.countBetweenDates('months', now(), license.start_of_day(v_latest_license_activity.valid_to_date));
		-- if the renewal is atlease more than one day late, then add the late fee
		v_late_days_count := EXTRACT(DAY FROM AGE(now(), license.start_of_day(v_latest_license_activity.valid_to_date)));
		if v_late_days_count > 0 then
			v_late_month_count := v_late_month_count + 1;
		end if;
		if v_late_month_count > 0 then
			RETURN QUERY
			select 
				'manual' as fee_type,
				'DL-M-LATE' as fee_code,
				case 
					when v_late_month_count * v_late_fee_rate > v_late_capped_amount then v_late_capped_amount
					else v_late_month_count * v_late_fee_rate
				end as fee_amount;
		end if;
	end if;

	-- payment processing fee
	-- RETURN QUERY
	-- select 
	-- 	'percentage' as fee_type,
	-- 	'PAY-PP-ONLINE' as fee_code,
	-- 	0.00 as fee_amount
	-- union ALL
	-- SELECT 
	-- 	'manual' as fee_type,
	-- 	'PAY-ONLINE' as fee_code,
	-- 	0.00 as fee_amount;


	if v_is_spayed_or_neutered then
		-- if the dog is spayed or neutered, add the DL-S-ALT fee
		RETURN QUERY
		select 
			'calculated' as fee_type,
			'DL-S-ALT' as fee_code,
			0.00 as fee_amount
		union ALL
		select 
			'manual' as fee_type,
			'DL-M-ALT' as fee_code,
			CASE 
				when v_is_renewal then v_renewal_spayed_neutered_cost 
			else (v_duration_months * v_spayed_neutered_rate)
			end as fee_amount;
	else
		-- if the dog is not spayed or neutered, add the DL-S-UNALT fees
		RETURN QUERY
		select 
			'calculated' as fee_type,
			'DL-S-UNALT' as fee_code,
			0.00 as fee_amount
		union ALL
		select 
			'manual' as fee_type,
			'DL-M-UNALT' as fee_code,
			case 
				when v_is_renewal then v_renewal_unspayed_unneutered_cost
			else (v_duration_months * v_unspayed_unneutered_rate)
			end as fee_amount;
	end if;
END;
$$;

update license_type lt
set fee_config_function = 'fn_dogLicenseFeeCalculation'
where lt.code = 'dogLicense';

--select * from fn_dogLicenseFeeCalculation('76bd2329-da1f-4414-99b8-b7a1699ef25d');
