drop FUNCTION if exists license.determineDogLicenseFeePreview;
CREATE OR REPLACE FUNCTION license.determineDogLicenseFeePreview(
    in i_license_entity_id uuid
)
RETURNS TABLE (
    fee_type text,
    fee_code text,
    fee_amount NUMERIC(20, 10),
    label text,
    duration int
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_max_duration int := 0;
    v_i int := 1;
    v_j int := 1;
    v_duration_months int := (18 - EXTRACT(MONTH FROM now()));
    v_license record;
    v_latest_license_activity record;
    v_is_renewal boolean := false;
    v_label text;
    v_duration int;
    v_total_months int;
    v_years int;
    v_remaining_months int;
BEGIN
    select 
        l.*
    into v_license
    from license.view_license l
    where l.license_type_code = 'dogLicense'
    and l.license_uuid = i_license_entity_id;
    
    if v_license is null then
        raise exception 'License with UUID % not found', i_license_entity_id;
    end if;

    select 
        la.*
    into v_latest_license_activity
    from license.license_activity la
    where la.license_id = v_license.license_id
    order by la.license_activity_id desc
    limit 1;
    
    v_is_renewal := (v_latest_license_activity.activity_type is not null);
    
    SELECT d.duration
    INTO v_max_duration
    FROM license.fn_determineDogLicenseDuration(i_license_entity_id) d;
    
    if NOT v_is_renewal THEN
        -- not renewals
        v_max_duration := v_max_duration - 1;
        v_total_months := v_duration_months;
        v_years := v_total_months / 12;
        v_remaining_months := v_total_months % 12;
        
        v_label := CASE 
            WHEN v_years > 0 THEN v_years || ' year' || CASE WHEN v_years > 1 THEN 's' ELSE '' END
            ELSE ''
        END ||
        CASE 
            WHEN v_remaining_months > 0 THEN 
                CASE WHEN v_years > 0 THEN ' and ' ELSE '' END ||
                v_remaining_months || ' month' || CASE WHEN v_remaining_months > 1 THEN 's' ELSE '' END
            ELSE ''
        END;
        
        RETURN QUERY
            SELECT 
                f.fee_type,
                f.fee_code,
                f.fee_amount,
                v_label AS label,
                1 AS duration
            FROM
                license.fn_dogLicenseFeeCalculation(i_license_entity_id) f;
    end if;
    
    FOR v_i IN 1..v_max_duration LOOP
        IF v_is_renewal THEN
            v_label := v_i || ' year' || CASE WHEN v_i > 1 THEN 's' ELSE '' END;
            v_duration := v_i;
        ELSE
            v_total_months := (v_i * 12) + v_duration_months;
            v_years := v_total_months / 12;
            v_remaining_months := v_total_months % 12;
            
            v_label := CASE 
                WHEN v_years > 0 THEN v_years || ' year' || CASE WHEN v_years > 1 THEN 's' ELSE '' END
                ELSE ''
            END ||
            CASE 
                WHEN v_remaining_months > 0 THEN 
                    CASE WHEN v_years > 0 THEN ' and ' ELSE '' END ||
                    v_remaining_months || ' month' || CASE WHEN v_remaining_months > 1 THEN 's' ELSE '' END
                ELSE ''
            END;
            
            v_duration := v_i + 1;
        END IF;

        IF NOT v_is_renewal THEN
            RETURN QUERY
            SELECT 
                f.fee_type,
                f.fee_code,
                f.fee_amount,
                v_label AS label,
                v_duration AS duration
            FROM
                license.fn_dogLicenseFeeCalculation(i_license_entity_id) f;
        END IF;

        FOR v_j in 1..v_i LOOP
            if v_is_renewal and v_j = 1 THEN
                RETURN QUERY
                SELECT 
                    f.fee_type,
                    f.fee_code,
                    f.fee_amount,
                    v_label AS label,
                    v_duration AS duration
                FROM
                    license.fn_dogLicenseFeeCalculation(i_license_entity_id, true, true) f;
            else
                RETURN QUERY
                SELECT 
                    f.fee_type,
                    f.fee_code,
                    f.fee_amount,
                    v_label AS label,
                    v_duration AS duration
                FROM 
                    license.fn_dogLicenseFeeCalculation(i_license_entity_id, true, FALSE) f;
            end if;
        END LOOP;
    END LOOP;
END;
$$;


--select * from license.determineDogLicenseFeePreview('76bd2329-da1f-4414-99b8-b7a1699ef25d');

update license_type lt 
set fee_preview_function = 'determineDogLicenseFeePreview'
where lt.code = 'dogLicense';