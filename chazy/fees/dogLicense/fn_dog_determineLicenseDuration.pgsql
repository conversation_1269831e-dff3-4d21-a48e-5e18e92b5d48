DROP FUNCTION IF EXISTS license.fn_determineDogLicenseDuration;
CREATE OR REPLACE FUNCTION license.fn_determineDogLicenseDuration(
    in i_license_entity_id uuid
)
RETURNS TABLE(
    duration INT,
    message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_license record;
    v_dog_properties jsonb;
    v_duration_months int := (18 - EXTRACT(MONTH FROM now()));

    v_vaccine_expiration_date date;
    v_license_expiration_date date;
    v_is_vaccine_exempt boolean;
    v_max_renewal_years int := 3;
    v_current_date date := CURRENT_DATE;
    v_future_current_date date;
    v_max_vaccine_expiration_date date;
    v_whichever_is_less date;
    v_years_until_exp int := 0;
BEGIN
    -- Fetch license information
    SELECT l.*
    INTO v_license
    FROM license.view_license l
    WHERE l.license_type_code = 'dogLicense'
    AND l.license_uuid = i_license_entity_id;

    IF v_license IS NULL THEN
        RAISE EXCEPTION 'License with UUID % not found', i_license_entity_id;
    END IF;

    -- Fetch dog properties
    SELECT pdog.properties
    INTO v_dog_properties
    FROM license.association aDog
    LEFT JOIN license.view_participant pdog
        ON pdog.participant_id = aDog.child_id
    WHERE aDog.parent_association_type = 'LICENSE'
    AND aDog.child_association_type = 'PARTICIPANT'
    AND aDog.parent_id = v_license.license_id
    AND pdog.group_name ILIKE 'Dog';

    IF v_dog_properties IS NULL THEN
        RAISE EXCEPTION 'Dog not found for license with UUID %', i_license_entity_id;
    END IF;

    -- Fetch vaccine information
    v_vaccine_expiration_date := (v_dog_properties->>'vaccineDueDate')::date;
    v_is_vaccine_exempt := COALESCE(v_dog_properties->>'vaccineDatesExempt','false') = 'true';
    v_license_expiration_date := COALESCE(v_license.valid_to_date, now());

    -- Calculate future current date (3 years from the current month end)
    v_future_current_date := (license.addYears(license.end_of_month(v_current_date), v_max_renewal_years))::date;

    -- Check if vaccine exempt, calculate based on license expiration
    IF v_is_vaccine_exempt THEN
        v_years_until_exp := GREATEST(
            LEAST(
                v_max_renewal_years, 
                DATE_PART('year', AGE(v_future_current_date, v_license_expiration_date))
            ), 
            0
        );
        IF v_years_until_exp = 0 THEN
            RETURN QUERY SELECT 0, 'Exempt from vaccine, but not eligible for renewal yet';
        ELSE
            RETURN QUERY SELECT v_years_until_exp, 'Exempt from vaccine, eligible for renewal';
        END IF;
    END IF;

    -- If vaccine expiration exists, calculate based on it
    IF v_vaccine_expiration_date IS NOT NULL THEN
        v_max_vaccine_expiration_date := (license.end_of_month(license.addMonths(v_vaccine_expiration_date, 11)))::date;

        -- Choose the lesser of max vaccine expiration date or future current date
        v_whichever_is_less := LEAST(v_max_vaccine_expiration_date, v_future_current_date);

        -- Calculate years until expiration
        v_years_until_exp := GREATEST(
            LEAST(v_max_renewal_years, 
                DATE_PART('year', AGE(v_whichever_is_less, v_license_expiration_date))
            ), 
            0
        );

        IF v_years_until_exp = 0 THEN
            RETURN QUERY SELECT 0, 'Vaccine expires too soon for renewal eligibility';
        ELSE
            RETURN QUERY SELECT v_years_until_exp, 'Vaccine valid, eligible for renewal';
        END IF;

    -- If No vaccine info at all
    ELSE
        RETURN QUERY SELECT 0, 'No vaccine info and not exempt';
    END IF;


END;
$$;


--select * from license.fn_determineDogLicenseDuration('76bd2329-da1f-4414-99b8-b7a1699ef25d');

update license.license_type lt
set duration_function = 'fn_determineDogLicenseDuration'
where lt.code = 'dogLicense';