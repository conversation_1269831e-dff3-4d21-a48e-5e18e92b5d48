create or replace function license.checkHouseHoldRule(
    IN v_individual_uuid varchar(36),
    IN v_household_count INT
)
RETURNS TABLE(
    fail BOOLEAN,
    error_message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_address_id INT;
    v_active_count INT;
    v_filtered_count INT;
BEGIN
    -- determine the home address of the individual
    select 
        vpa.address_id
    into v_address_id
    from license.view_participant p
    inner join license.view_participant_address vpa
    on vpa.participant_id = p.participant_id
    where p.entity_id = v_individual_uuid::uuid
    and vpa.address_type ilike 'home'
    limit 1;

    -- get a list of household licenses
    create temp table v_household_licenses as
    select
        l.*,
        lt.code as type_code,
        ls.code as status_code
    from license.address addr
    inner join license.association aAddr
        on aAddr.parent_association_type = 'ADDRESS'
        and aAddr.parent_id = addr.address_id 
    inner join license.view_participant p
        on p.participant_id = aAddr.child_id 
        and aAddr.child_association_type = 'PARTICIPANT'
    inner join license.association aLic
        on aLic.parent_association_type = 'PARTICIPANT'
        and aLic.parent_id = p.participant_id
    inner join license.license l
        on l.license_id = aLic.child_id 
        and aLic.child_association_type = 'LICENSE'
    inner join license.license_type lt 
        on lt.license_type_id  = l.license_type_id 
    inner join license.license_status ls 
        on ls.license_status_id  = l.license_status_id
    where p.group_name ILIKE 'Individual'
    and lt.code in ('dogLicense')
    and addr.address_id = v_address_id;

    -- count the number of active licenses
    select count(*)
        into v_active_count
    from v_household_licenses
    where status_code in ('ACTIVE');

    -- count the number of licenses that counts towards the household count
    select count(*)
        into v_filtered_count
    from v_household_licenses
    where status_code in ('ACTIVE', 'EXPIRED', 'PENDING_APPROVAL');

    drop table v_household_licenses;

-- Check conditions and return appropriate message
    IF v_active_count >= v_household_count THEN
        RETURN QUERY SELECT TRUE AS fail, 'A property cannot have more than '||v_household_count||' active licenses.' AS error_message;
    ELSIF v_filtered_count >= v_household_count THEN
        RETURN QUERY SELECT TRUE AS fail, 'A property cannot have more than '||v_household_count||' licenses. Cancelled or renew existing licenses or apply for a purebred dog license.' AS error_message;
    ELSE
        RETURN QUERY SELECT FALSE AS fail, '' AS error_message;
    END IF;
END;
$$;

--select * from license.checkHouseHoldRule(:v_individual_uuid, 3);


-- update sql_storage
-- set sql_query = 'select * from license.checkHouseHoldRule(:v_individual_uuid, 5);'
-- where name = 'checkHouseHoldRule';

-- update sql_storage
-- set sql_query = 'select * from license.checkHouseHoldRule(:entity_id, 5);'
-- where name = 'dogHouseholdLimit';