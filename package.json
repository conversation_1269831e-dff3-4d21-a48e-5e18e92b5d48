{"name": "backoffice", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:https": "node server.js", "build": "next build", "start": "next start", "lint": "next lint", "knip": "knip", "clean": "rimraf .next node_modules && npm install", "standalone": "next build && cp -r public/ .next/standalone && cp -r .next/static .next/standalone/.next"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.2", "@googlemaps/react-wrapper": "^1.1.35", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^4.1.3", "@hotjar/browser": "^1.0.9", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.0.7", "@react-pdf-viewer/core": "^3.12.0", "@stripe/stripe-js": "^4.10.0", "@tanstack/react-query": "^4.29.25", "@tanstack/react-query-devtools": "^4.35.7", "@tanstack/react-table": "^8.10.7", "@tanstack/table-core": "^8.9.1", "@types/react": "18.0.27", "@types/react-dom": "18.0.10", "@xstate/react": "^4.0.3", "axios": "^1.8.4", "backoffice": "file:", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.1", "d3": "^7.9.0", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dompurify": "^3.0.8", "embla-carousel-react": "^8.0.0-rc23", "eslint": "8.32.0", "framer-motion": "^9.0.3", "geist": "^1.3.0", "input-otp": "^1.2.4", "jotai": "^2.0.2", "kbar": "^0.1.0-beta.44", "keycloak-js": "^25.0.2", "lucide-react": "^0.468.0", "next": "14.0.0", "node-redmine": "^0.2.2", "novel": "^0.3.1", "openai": "^3.1.0", "pdfjs-dist": "^3.6.172", "quill": "^1.3.7", "react": "^18.2.0", "react-confetti": "^6.4.0", "react-day-picker": "^8.10.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-hook-form": "^7.54.2", "react-icons": "^4.10.1", "react-markdown": "^9.0.1", "react-quill": "^2.0.0", "react-scan": "^0.2.4", "react-select": "^5.8.3", "react-simple-maps": "^3.0.0", "react-textarea-autosize": "^8.5.4", "reactflow": "^11.11.4", "remark-gfm": "^4.0.0", "sharp": "^0.32.6", "tailwind-merge": "^1.14.0", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^2.1.1", "use-debounce": "^10.0.0", "vaul": "^0.8.0", "xstate": "^5.6.0", "yup": "^1.4.0", "zod": "^3.24.2"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "@svgr/webpack": "^6.5.1", "@types/dompurify": "^3.0.5", "@types/google.maps": "^3.53.5", "@types/node": "^22.3.0", "@types/react-pdf": "^6.2.0", "@types/react-simple-maps": "^3.0.6", "@types/react-textarea-autosize": "^8.0.0", "autoprefixer": "^10.4.17", "devcert": "^1.2.2", "eslint-config-next": "^13.5.2", "knip": "^5.27.2", "postcss": "^8.4.34", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "react-pdf": "^6.2.2", "tailwindcss": "^3.4.1", "typescript": "^5.5.4"}}