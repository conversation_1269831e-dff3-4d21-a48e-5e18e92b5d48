#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 9008
ENV ASPNETCORE_URLS=http://*:9008
ENV ASPNETCORE_ENVIRONMENT Development

RUN apt-get update -y && apt-get install --no-install-recommends -y git cmake build-essential pkg-config && mkdir leptonica
RUN git clone https://github.com/DanBloomberg/leptonica.git /leptonica

WORKDIR /leptonica
RUN git checkout -b 1.82.0 1.82.0
RUN mkdir build
WORKDIR /leptonica/build
RUN cmake ..
RUN apt-get install --no-install-recommends -y libtesseract-dev tesseract-ocr

WORKDIR /app/x64
RUN ln -s /usr/lib/x86_64-linux-gnu/liblept.so.5 liblept.so.5
RUN ln -s /usr/lib/x86_64-linux-gnu/liblept.so.5 libleptonica-1.82.0.so
RUN ln -s /usr/lib/x86_64-linux-gnu/libtesseract.so.4 libtesseract50.so

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY ["Service_OCR/Service_OCR.csproj", "Service_OCR/"]
RUN dotnet restore "Service_OCR/Service_OCR.csproj"
COPY . .
WORKDIR "/src/Service_OCR"
RUN dotnet build "Service_OCR.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "Service_OCR.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Service_OCR.dll"]

