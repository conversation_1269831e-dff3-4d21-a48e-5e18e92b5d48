name: Run npm ci & lint

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

on:
  workflow_dispatch:
  pull_request:
    branches: ["main"]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: 19

jobs:
  lint:
    runs-on: ubuntu-latest
    environment: development
    permissions:
      contents: read
      packages: write
      id-token: write

    # Refer to base documentation for more info for the follow steps: https://github.com/aws-actions/amazon-ecr-login
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Cache npm dependencies
        uses: actions/cache@v4
        with:
          path: '~/.npm'
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Run npm install
        run: npm install

      - name: Run eslint install
        run: npm install eslint

      - name: Run ESLint
        run: npm run lint

      - name: NextJS
        run: rm -rf .next

      - name: Clear Cache
        run: npm cache clear --force

  build:
    needs: lint
    runs-on: ubuntu-latest
    environment: development
    permissions:
      contents: read
      packages: write
      id-token: write

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Cache npm dependencies
        uses: actions/cache@v4
        with:
          path: '~/.npm'
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Run npm install
        run: npm install

      - name: Make development envfile
        uses: SpicyPizza/create-envfile@v2.0
        with:
          envkey_NEXT_PUBLIC_APP_ENV: "development"
          envkey_NEXT_PUBLIC_API_BACKEND_HOST: ${{ secrets.API_BACKEND_HOST }}
          envkey_NEXT_PUBLIC_MAP_API_KEY: ${{ secrets.MAP_API_KEY }}
          envkey_NEXT_PUBLIC_MAP_ID: ${{ secrets.MAP_ID }}
          envkey_NEXT_PUBLIC_KEYCLOAK: ${{ secrets.KEYCLOAK }}
          envkey_NEXT_PUBLIC_REDMINE_API_KEY: ${{ secrets.REDMINE_API_KEY }}
          envkey_REDMINE_API_KEY: ${{ secrets.REDMINE_API_KEY }}
          envkey_REDMINE_HOST: ${{ secrets.REDMINE_HOST }}
          file_name: .env
          fail_on_empty: false

      - name: Run npm run build
        run: npm run build

      - name: Clear NextJS artifacts
        run: rm -rf .next

      - name: Clear cache
        run: npm cache clear --force
