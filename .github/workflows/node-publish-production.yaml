name: "Production: Update, Build & Deploy"
run-name: Deploy ${{ github.repository }} ${{ github.ref_name }} to Production by @${{ github.actor }}

on:
  push:
    # Publish semver tags as releases.
    tags: ["v*.*.*"]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: backoffice_website
  EKS_DEPLOYMENT: scube-backoffice-service-depl
  EKS_CONTAINER: scube-backoffice-service
  KUBE_NAMESPACE: frontend
  IMAGE_TAG: ${{ github.ref_name }}
  NODE_VERSION: 19

jobs:
  build:
    runs-on: ubuntu-latest
    environment: production
    permissions:
      contents: read
      packages: write
      id-token: write
      deployments: write

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_REGION }}
          role-to-assume: ${{ secrets.AWS_OIDC_GITHUB_ROLE }}
          role-session-name: ci-run-${{ github.run_id }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Cache npm dependencies
        uses: actions/cache@v4
        with:
          path: "~/.npm"
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Make production envfile
        uses: SpicyPizza/create-envfile@v2.0
        with:
          envkey_NEXT_PUBLIC_APP_ENV: "production"
          envkey_CLERKXPRESS_REALM: ${{ secrets.CLERKXPRESS_REALM }}
          envkey_NEXT_PUBLIC_CLERKXPRESS_REALM: ${{ secrets.CLERKXPRESS_REALM }}
          envkey_NEXT_PUBLIC_API_BACKEND_HOST: ${{ secrets.API_BACKEND_HOST }}
          envkey_NEXT_PUBLIC_MAP_API_KEY: ${{ secrets.MAP_API_KEY }}
          envkey_NEXT_PUBLIC_MAP_ID: ${{ secrets.MAP_ID }}
          envkey_NEXT_PUBLIC_KEYCLOAK: ${{ secrets.KEYCLOAK }}
          envkey_REDMINE_API_KEY: ${{ secrets.REDMINE_API_KEY }}
          envkey_REDMINE_HOST: ${{ secrets.REDMINE_HOST }}
          file_name: .env
          fail_on_empty: false

      # Container environmental variables go here
      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: ./
          file: ./Dockerfile
          push: true
          tags: ${{ secrets.AWS_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ env.IMAGE_TAG }}
          build-args: |
            APP_ENV=production
          cache-from: type=registry,ref=${{ secrets.AWS_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ env.IMAGE_TAG }}
          cache-to: type=inline,mode=min

  deploy:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    permissions:
      contents: read
      packages: write
      id-token: write
      deployments: write

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_REGION }}
          role-to-assume: ${{ secrets.AWS_OIDC_GITHUB_ROLE }}
          role-session-name: ci-run-${{ github.run_id }}

      - name: Set new version to Kubernetes cluster
        uses: kodermax/kubectl-aws-eks@main
        env:
          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_DATA }}
          RELEASE_IMAGE: ${{ secrets.AWS_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ env.IMAGE_TAG }}
        with:
          args: set image deployment/${{ env.EKS_DEPLOYMENT }} ${{ env.EKS_CONTAINER }}=${{ env.RELEASE_IMAGE }} --record -n ${{ env.KUBE_NAMESPACE }}

      - name: Rollout deployment
        uses: kodermax/kubectl-aws-eks@main
        with:
          args: rollout restart deployment/${{ env.EKS_DEPLOYMENT }} -n ${{ env.KUBE_NAMESPACE }}
        env:
          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_DATA }}

      - name: Verify deployment
        uses: kodermax/kubectl-aws-eks@main
        with:
          args: rollout status deployment/${{ env.EKS_DEPLOYMENT }} -n ${{ env.KUBE_NAMESPACE }}
        env:
          KUBE_CONFIG_DATA: ${{ secrets.KUBE_CONFIG_DATA }}

      - name: Microsoft Teams Notification
        uses: jdcargile/ms-teams-notification@v1.4
        if: ${{ failure() }}
        with:
          github-token: ${{ github.token }} # this will use the runner's token.
          ms-teams-webhook-uri: ${{ secrets.MS_TEAMS_WEBHOOK_URI }}
          notification-summary: "Build & Deploy for ${{ github.repository }} ${{ github.ref_name }} by @${{ github.actor }} failed"
          notification-color: dc3545
          timezone: America/New_York
