name: "Production: Update, Build & Deploy"
run-name: Deploy ${{ github.repository }} ${{ github.ref_name }} to Production by @${{ github.actor }}

on:
  push:
    # Publish semver tags as releases.
    tags: ["v*.*.*"]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
  
jobs:
  production-build-deploy:
    uses: s-Cube-Enterprise/reusable-workflows/.github/workflows/docker-build-publish.yaml@main
    with:
      AWS_REGION: us-east-1
      ECR_REPOSITORY: service_ocr
      EKS_DEPLOYMENT: scube-ocr-service-depl
      EKS_CONTAINER: scube-ocr-service
      KUBE_NAMESPACE: backend
      IMAGE_TAG: ${{ github.ref_name }}
      TARGET_ENVIRONMENT: PROD
    secrets: inherit
