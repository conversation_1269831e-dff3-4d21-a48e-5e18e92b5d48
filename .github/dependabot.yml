version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "daily"
    labels:
      - "dependabot"
    registries:
      - github
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "monthly"
      day: "sunday"
      time: "21:00"
    labels:
      - "dependabot"
registries:
  github:
    type: git
    url: https://github.com
    username: x-access-token # username doesn't matter
    password: ${{ secrets.DEPENDABOT_TOKEN }} # dependabot secret
