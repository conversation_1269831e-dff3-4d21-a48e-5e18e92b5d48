drop FUNCTION if exists license.determineDogLicenseFeePreview;
CREATE OR REPLACE FUNCTION license.determineDogLicenseFeePreview(
    in i_license_entity_id uuid
)
RETURNS TABLE (
    fee_type text,
    fee_code text,
    fee_amount NUMERIC(20, 10),
    label text,
    start_year int,
    end_year int
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_max_duration int := 0;
    v_i int := 1;
    v_j int := 1;
    v_license record;
	v_latest_license_activity record;
    v_is_renewal boolean := false;
    v_valid_to_date TIMESTAMP;
	v_valid_from_date TIMESTAMP;
    v_label text;
    v_duration int;
    v_current_date date := CURRENT_DATE;
    v_start_year int := EXTRACT(YEAR FROM v_current_date);
    v_end_year int := EXTRACT(YEAR FROM v_current_date) + 1;

    -- open registration for next year is june 1st to june 30th of the current year
    v_open_registration_start_date date := (EXTRACT(YEAR FROM v_current_date) || '-06-01')::date;
    v_open_registration_end_date date := (EXTRACT(YEAR FROM v_current_date) || '-06-30')::date;
    v_is_open_registration boolean := v_current_date BETWEEN v_open_registration_start_date AND v_open_registration_end_date;
    v_is_before_open_registration boolean := v_current_date < v_open_registration_start_date;
    v_is_after_open_registration boolean := v_current_date > v_open_registration_end_date;

    v_exclude_renewal_condition boolean := false;
BEGIN
    select 
    	l.*
	into v_license
	from license.view_license l
	where l.license_type_code = 'dogLicense'
	and l.license_uuid = i_license_entity_id;

	if v_license is null then
        raise exception 'License with UUID % not found', i_license_entity_id;
    end if;

	select 
		la.*
	into v_latest_license_activity
	from license.license_activity la
	where la.license_id = v_license.license_id
	order by la.license_activity_id desc
	limit 1;

	v_is_renewal := (v_latest_license_activity.activity_type is not null);
    v_valid_to_date := COALESCE(v_latest_license_activity.valid_to_date, v_license.valid_to_date);
    v_valid_from_date := COALESCE(v_latest_license_activity.valid_from_date, v_license.valid_from_date);

    SELECT d.duration
    INTO v_max_duration
    FROM license.fn_determineDogLicenseDuration(i_license_entity_id) d;

    -- if the current date is before July 1st, set the start year to the previous year
    -- otherwise, set the start year to the current year
    IF v_is_before_open_registration OR v_is_open_registration THEN
        v_start_year := v_start_year - 1;
        v_end_year := v_end_year - 1;
    END IF;

    -- exclude if v_is_before_open_registration and v_valid_to_date after the open registration start date
    v_exclude_renewal_condition := v_is_renewal AND v_is_before_open_registration AND v_valid_to_date > v_open_registration_start_date;

    RAISE NOTICE 'v_exclude_renewal_condition: %', v_exclude_renewal_condition;
    RAISE NOTICE 'v_is_renewal: %', v_is_renewal;
    RAISE NOTICE 'v_is_before_open_registration: %', v_is_before_open_registration;
    RAISE NOTICE 'v_is_open_registration: %', v_is_open_registration;
    RAISE NOTICE 'v_is_after_open_registration: %', v_is_after_open_registration;
    RAISE NOTICE 'v_start_year: %', v_start_year;
    RAISE NOTICE 'v_end_year: %', v_end_year;
    RAISE NOTICE 'v_valid_to_date: %', v_valid_to_date;
    RAISE NOTICE 'v_valid_from_date: %', v_valid_from_date;
    RAISE NOTICE 'v_current_date: %', v_current_date;
    RAISE NOTICE 'v_max_duration: %', v_max_duration;
    RAISE NOTICE 'v_open_registration_start_date: %', v_open_registration_start_date;
    RAISE NOTICE 'v_open_registration_end_date: %', v_open_registration_end_date;

    if not v_exclude_renewal_condition then
        -- if it is a new license
        IF v_is_open_registration THEN
            -- if the current date is between the open registration start date and end date
            -- return the next year
            RETURN QUERY
            SELECT 
                f.fee_type,
                f.fee_code,
                f.fee_amount,
                FORMAT('July 1, %s - June 30, %s', v_start_year + 1, v_end_year + 1) AS label,
                v_start_year  + 1 AS start_year,
                v_end_year + 1 AS end_year
            FROM
                license.fn_dogLicenseFeeCalculation(i_license_entity_id, v_is_renewal, v_i = 1) f;
        END IF;
        
        FOR v_i IN 1..v_max_duration LOOP
            v_duration := v_i;         
            v_label := FORMAT('July 1, %s - June 30, %s', v_start_year, v_end_year + v_i -1);

            FOR v_j in 1..v_i LOOP
                -- if their license is already have an expiration date of the endYear then they can't renew for that again
                IF v_valid_to_date <= (v_end_year + v_i - 1 || '-06-30')::date THEN
                    RETURN QUERY
                    SELECT 
                        f.fee_type,
                        f.fee_code,
                        f.fee_amount,
                        v_label AS label,
                        v_start_year AS start_year,
                        v_end_year + v_i - 1 AS end_year
                    FROM
                        license.fn_dogLicenseFeeCalculation(i_license_entity_id, v_is_renewal, v_j = 1) f;
                END IF;
            END LOOP;
        END LOOP;
    END IF;
        
END $$;


--select * from license.determineDogLicenseFeePreview('96741af6-9abf-4668-afae-45ba72b01090');

update license_type lt 
set fee_preview_function = 'determineDogLicenseFeePreview'
where lt.code = 'dogLicense';