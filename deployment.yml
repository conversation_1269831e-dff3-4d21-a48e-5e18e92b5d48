apiVersion: apps/v1
kind: Deployment
metadata:
  name: scube-ocr-service-depl
  labels:
    app: scube-ocr-service
spec:
  selector:
    matchLabels:
      app: scube-ocr-service
  template:
    metadata:
      labels:
        app: scube-ocr-service
    spec:
      imagePullSecrets:
        - name: aws-ecr-secret
      containers:
        - name: scube-ocr-service
          image: service_ocr
          ports:
            - containerPort: 9008
          resources:
            requests:
              memory: "350Mi"
            limits:
              memory: "1Gi"
          readinessProbe:
            httpGet:
              path: /api/ocr/actuator/health
              port: 9008
            initialDelaySeconds: 30
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 9008
            initialDelaySeconds: 60
            periodSeconds: 30
          env:
            - name: TZ
              value: "America/New_York"
---
apiVersion: v1
kind: Service
metadata:
  name: scube-ocr-service-srv
  labels:
    app: scube-ocr-service
spec:
  selector:
    app: scube-ocr-service
  type: ClusterIP
  ports:
    - name: ocr-port
      protocol: TCP
      port: 9008
      targetPort: 9008
