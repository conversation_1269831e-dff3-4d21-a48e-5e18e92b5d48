apiVersion: apps/v1
kind: Deployment
metadata:
  name: scube-backoffice-service-depl
  namespace: frontend
  labels:
    app: scube-backoffice-service
spec:
  selector:
    matchLabels:
      app: scube-backoffice-service
  template:
    metadata:
      labels:
        app: scube-backoffice-service
    spec:
      nodeSelector:
        subnet-type: private
      imagePullSecrets:
        - name: aws-ecr-secret
      containers:
        - name: scube-backoffice-service
          image: backoffice_website
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          readinessProbe:
            httpGet:
              path: /login
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /login
              port: 3000
            initialDelaySeconds: 60
            periodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  name: scube-backoffice-service-srv
  namespace: frontend
  labels:
    app: scube-backoffice-service
spec:
  selector:
    app: scube-backoffice-service
  type: ClusterIP
  ports:
    - name: backoffice-port
      protocol: TCP
      port: 3000
      targetPort: 3000
