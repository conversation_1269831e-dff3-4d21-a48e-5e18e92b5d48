apiVersion: apps/v1
kind: Deployment
metadata:
  name: scube-document-template-helper-service-depl
  labels:
    app: scube-document-template-helper-service
spec:
  selector:
    matchLabels:
      app: scube-document-template-helper-service
  template:
    metadata:
      labels:
        app: scube-document-template-helper-service
    spec:
      imagePullSecrets:
        - name: aws-ecr-secret
      containers:
        - name: scube-document-template-helper-service
          image: service_document_template_helper
          ports:
            - containerPort: 9012
          resources:
            requests:
              memory: "350Mi"
            limits:
              memory: "1Gi"
          readinessProbe:
            httpGet:
              path: /api/document-template-helper/actuator/health
              port: 9012
            initialDelaySeconds: 30
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 9012
            initialDelaySeconds: 60
            periodSeconds: 30
          env:
            - name: TZ
              value: "America/New_York"
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: postgres-user-pass-secret
                  key: username
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-user-pass-secret
                  key: password
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: username
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: password
---
apiVersion: v1
kind: Service
metadata:
  name: scube-document-template-helper-service-srv
  labels:
    app: scube-document-template-helper-service
spec:
  selector:
    app: scube-document-template-helper-service
  type: ClusterIP
  ports:
    - name: temp-help-port
      protocol: TCP
      port: 9012
      targetPort: 9012
