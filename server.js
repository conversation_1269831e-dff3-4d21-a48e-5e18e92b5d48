const devcert = require("devcert");
const { createServer } = require("https");
const { parse } = require("url");
const next = require("next");

const port = parseInt(process.env.PORT, 10) || 3000;
const dev = process.env.APP_ENV !== "production";
const app = next({ dev });
const handle = app.getRequestHandler();

(async () => {
  try {
    const ssl = await devcert.certificateFor("localhost");
    app.prepare().then(() => {
      createServer({ key: ssl.key, cert: ssl.cert }, (req, res) => {
        const parsedUrl = parse(req.url, true);
        handle(req, res, parsedUrl);
      }).listen(port, (err) => {
        if (err) throw err;
        console.log(`> Ready on https://localhost:${port}`);
      });
    });
  } catch (error) {
    console.error("> Error setting up dev SSL certificate:", error);
  }
})();
