/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  reactStrictMode: true,
  images: {
    domains: [
      "randomuser.me",
      "images.dog.ceo",
      "photos.zillowstatic.com",
      "image.chewy.com",
      "cms-dev.clerkxpress.com",
      "cms-staging.clerkxpress.com",
      "cms.clerkxpress.com",
      "clerkxpress-public.s3.us-east-1.amazonaws.com"
    ],
  },
  env: {
    NEXT_PUBLIC_API_BACKEND_HOST: process.env.NEXT_PUBLIC_API_BACKEND_HOST,
    NEXT_PUBLIC_MAP_API_KEY: process.env.NEXT_PUBLIC_MAP_API_KEY,
    NEXT_PUBLIC_MAP_ID: process.env.NEXT_PUBLIC_MAP_ID,
    NEXT_PUBLIC_KEYCLOAK: process.env.NEXT_PUBLIC_KEYCLOAK,
  },
  webpack: (config) => {
    config.externals.push({ canvas: "commonjs canvas" });
    config.resolve.alias.canvas = false;
    return config;
  },
  compiler: {
    removeConsole:
      process.env.NODE_ENV === "production"
        ? {
            exclude: ["error", "warn"],
          }
        : false,
  },
  poweredByHeader: false,
};

module.exports = nextConfig;
