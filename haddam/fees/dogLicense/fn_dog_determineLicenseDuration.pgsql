DROP FUNCTION IF EXISTS license.fn_determineDogLicenseDuration(i_license_entity_id uuid);
CREATE OR REPLACE FUNCTION license.fn_determineDogLicenseDuration(
    in i_license_entity_id uuid
)
RETURNS TABLE(
    duration INT,
    message TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_license record;
    v_dog_properties jsonb;

    v_vaccine_expiration_date date;
    v_license_expiration_date date;
    v_is_vaccine_exempt boolean;
    v_max_renewal_years int := 1;
    v_current_date date := CURRENT_DATE;
    v_future_current_date date;
    v_max_vaccine_expiration_date date;
    v_whichever_is_less date;
    v_years_until_exp int := 0;
    v_duration int;
    v_message text;
    v_required_vaccine_valid_until date := (EXTRACT(YEAR FROM v_current_date) || '-06-01')::date;

    -- open registration for next year is june 1st to june 30th of the current year
    v_open_registration_start_date date := (EXTRACT(YEAR FROM v_current_date) || '-06-01')::date;
    v_open_registration_end_date date := (EXTRACT(YEAR FROM v_current_date) || '-06-30')::date;
    v_is_open_registration boolean := v_current_date BETWEEN v_open_registration_start_date AND v_open_registration_end_date;
BEGIN
    -- Fetch license information
    SELECT l.*
    INTO v_license
    FROM license.view_license l
    WHERE l.license_type_code = 'dogLicense'
    AND l.license_uuid = i_license_entity_id;

    IF v_license IS NULL THEN
        RAISE EXCEPTION 'License with UUID % not found', i_license_entity_id;
    END IF;

    -- if the today's date is between the open registration start date and end date, set the max renewal years to 2
    IF v_is_open_registration THEN
        v_max_renewal_years := 1;
    END IF;
    RAISE NOTICE 'v_max_renewal_years: %', v_max_renewal_years;

    -- Fetch dog properties
    SELECT pdog.properties
    INTO v_dog_properties
    FROM license.association aDog
    LEFT JOIN license.view_participant pdog
        ON pdog.participant_id = aDog.child_id
    WHERE aDog.parent_association_type = 'LICENSE'
    AND aDog.child_association_type = 'PARTICIPANT'
    AND aDog.parent_id = v_license.license_id
    AND pdog.group_name ILIKE 'Dog'
    order by pdog.properties->>'vaccineDueDate' ASC
    LIMIT 1;


    IF v_dog_properties IS NULL THEN
        RAISE EXCEPTION 'Dog not found for license with UUID %', i_license_entity_id;
    END IF;

    -- Fetch vaccine information
    v_vaccine_expiration_date := (v_dog_properties->>'vaccineDueDate')::date;
    v_is_vaccine_exempt := COALESCE(v_dog_properties->>'vaccineDatesExempt','false') = 'true';
    v_license_expiration_date := COALESCE(v_license.valid_to_date, v_current_date);

    -- Calculate future current date (3 years from the current month end)
    v_future_current_date := make_date(
        EXTRACT(YEAR FROM license.addYears(license.end_of_month(v_current_date), v_max_renewal_years))::int,
        6, -- June
        30
    );


    RAISE NOTICE 'v_max_renewal_years: %', v_max_renewal_years;
    RAISE NOTICE 'v_future_current_date: %', v_future_current_date;
    RAISE NOTICE 'v_license_expiration_date: %', v_license_expiration_date;
    RAISE NOTICE 'v_vaccine_expiration_date: %', v_vaccine_expiration_date;

    IF v_is_vaccine_exempt THEN
        v_duration := 1;
        v_message := 'Exempt from vaccine, eligible for renewal';
    
    ELSIF v_is_open_registration THEN
        v_duration := v_max_renewal_years;
        v_message := 'Open registration period: eligible for up to ' || v_max_renewal_years || '-year renewal';

    ELSE
        v_duration := 1;
        v_message := 'Vaccine valid, eligible for renewal';

    END IF;

    IF v_vaccine_expiration_date < v_current_date THEN
        v_duration := 0;
        v_message := 'Vaccine expired, not eligible for renewal';

    ELSIF v_vaccine_expiration_date < v_required_vaccine_valid_until THEN
        v_duration := 0;
        v_message := 'Vaccine must be valid through July 1 to be eligible for renewal';

    ELSIF v_vaccine_expiration_date IS NULL THEN
        v_duration := 0;
        v_message := 'No vaccine info and not exempt';
    END IF;

    RETURN QUERY SELECT v_duration, v_message;

END;
$$;

--select * from license.fn_determineDogLicenseDuration('96741af6-9abf-4668-afae-45ba72b01090');

update license.license_type lt 
set duration_function = 'fn_determineDogLicenseDuration'
where lt.code = 'dogLicense';