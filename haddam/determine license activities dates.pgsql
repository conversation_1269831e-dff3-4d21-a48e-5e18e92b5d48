--drop function determineLicenseActivityDate;
create or replace function license.determineLicenseActivityDate(
    IN v_license_uuid uuid
)
RETURNS TABLE(
    valid_from_date TIMESTAMP,
    valid_to_date TIMESTAMP
)
LANGUAGE plpgsql
AS $$
declare
	v_license record;
    v_latest_activity record;
	v_start_year int;
	v_end_year int;
	v_found boolean;
begin
	-- lookup the license
	select 
		l.*
	into v_license
	from license.license l
	where l.license_uuid = v_license_uuid;

    if v_license is null then
        raise exception 'License with UUID % not found', v_license_uuid;
    end if;

	v_start_year := (v_license.properties->>'startYear')::int;
	v_end_year := (v_license.properties->>'endYear')::int;

	IF v_start_year IS NULL OR v_end_year IS NULL THEN
		RAISE EXCEPTION 'Start year or end year is null for license with UUID %', v_license_uuid;
	end if;

	SELECT 
		true as found
	into v_found
	from license.determineDogLicenseFeePreview(v_license.license_uuid)
	where start_year = v_start_year
	and end_year = v_end_year;

	if not v_found then
		RAISE EXCEPTION 'License with UUID % not found for the given start and end year', v_license_uuid;
	end if;

	-- date is always 07/01/YYYY and 06/30/YYYY
	RETURN QUERY
		SELECT
			license.start_of_day(concat('07/01/', v_start_year::TEXT)::TIMESTAMP)::TIMESTAMP as valid_from_date,
			license.end_of_day(concat('06/30/', v_end_year::TEXT)::TIMESTAMP)::TIMESTAMP as valid_to_date;
END;
$$;

--existing
--select * from determineLicenseActivityDate('1c05a167-423b-45de-a9c2-7489185b049b');

--new
--select * from determineLicenseActivityDate('d5d5623e-0742-4eae-a752-7ca29f1aefc7');