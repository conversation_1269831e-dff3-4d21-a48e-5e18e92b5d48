INSERT INTO config.json_storage (
  properties,
  created_by,
  created_date,
  last_modified_by,
  json_data,
  last_modified_date
)
VALUES (
  '{"payment": "paymentRedirectType"}'::jsonb,
  'service-account-clerkxpress-seed', 
  CURRENT_TIMESTAMP, 
  'service-account-clerkxpress-seed',
  '{
    "Elavon": {
        "id": "formElavon",
        "sandbox": "https://api.demo.convergepay.com/hosted-payments",
        "inputToken": "ssl_txn_auth_token",
        "production": "https://api.demo.convergepay.com/hosted-payments"
    },
    "Authorize.Net": {
        "id": "formAuthorizeNetRedirect",
        "sandbox": "https://test.authorize.net/payment/payment",
        "inputToken": "token",
        "production": "https://accept.authorize.net/payment/payment"
    },
    "HOSTED_PAYMENT": [
        "Authorize.Net",
        "Elavon"
    ],
    "STRIPE_PAYMENT": [
        "Stripe"
    ]
}'::jsonb,  
  CURRENT_TIMESTAMP 
)
ON CONFLICT (properties)
DO UPDATE SET
  json_data = EXCLUDED.json_data,
  last_modified_by = EXCLUDED.last_modified_by,
  last_modified_date = CURRENT_TIMESTAMP;