{"all": [{"fieldName": "entityId", "columnName": "uuid", "isProperties": false, "searchType": "text"}, {"fieldName": "createdDate", "isProperties": false, "searchType": "date_range"}, {"fieldName": "created<PERSON>y", "isProperties": false, "searchType": "text"}, {"fieldName": "lastModifiedDate", "isProperties": false, "searchType": "date_range"}, {"fieldName": "lastModifiedBy", "isProperties": false, "searchType": "text"}], "individual": [{"fieldName": "status", "columnName": "participantStatus.name", "isProperties": false, "searchType": "text"}, {"fieldName": "groupName", "columnName": "participantTypeGroup.participantGroup.name", "isProperties": false, "searchType": "text"}, {"fieldName": "participantType", "columnName": "participantTypeGroup.participantType.name", "isProperties": false, "searchType": "text"}, {"fieldName": "contactValue", "columnName": "contacts.value", "isProperties": false, "searchType": "text"}, {"fieldName": "contactType", "columnName": "contacts.contactTypeGroup.contactType.name", "isProperties": false, "searchType": "text"}, {"fieldName": "contactGroup", "columnName": "contacts.contactTypeGroup.contactGroup.name", "isProperties": false, "searchType": "text"}, {"fieldName": "child.license.uuid", "columnName": "childAssociations.childLicense.uuid", "isProperties": false, "searchType": "text"}, {"fieldName": "firstName", "isProperties": true, "searchType": "text"}, {"fieldName": "lastName", "isProperties": true, "searchType": "text"}, {"fieldName": "dateOfBirth", "isProperties": true, "searchType": "date_range"}, {"fieldName": "registrationCode", "isProperties": true, "searchType": "text"}], "dog": [{"fieldName": "status", "columnName": "participantStatus.name", "isProperties": false, "searchType": "text"}, {"fieldName": "groupName", "columnName": "participantTypeGroup.participantGroup.name", "isProperties": false, "searchType": "text"}, {"fieldName": "participantType", "columnName": "participantTypeGroup.participantType.name", "isProperties": false, "searchType": "text"}, {"fieldName": "dogSex", "isProperties": true, "searchType": "text"}, {"fieldName": "<PERSON><PERSON><PERSON>", "isProperties": true, "searchType": "text"}, {"fieldName": "dogBreed", "isProperties": true, "searchType": "text"}, {"fieldName": "tagNumber", "isProperties": true, "searchType": "text"}, {"fieldName": "dogMarkings", "isProperties": true, "searchType": "text"}, {"fieldName": "dogBirthDate", "isProperties": true, "searchType": "date_range"}, {"fieldName": "vaccineBrand", "isProperties": true, "searchType": "text"}, {"fieldName": "vaccineDueDate", "isProperties": true, "searchType": "date_range"}, {"fieldName": "veterinaryName", "isProperties": true, "searchType": "text"}, {"fieldName": "dogPrimaryColor", "isProperties": true, "searchType": "text"}, {"fieldName": "microchipNumber", "isProperties": true, "searchType": "text"}, {"fieldName": "rabiesTagNumber", "isProperties": true, "searchType": "text"}, {"fieldName": "vaccineApproved", "isProperties": true, "searchType": "boolean"}, {"fieldName": "vaccineProducer", "isProperties": true, "searchType": "text"}, {"fieldName": "behaviorApproved", "isProperties": true, "searchType": "boolean"}, {"fieldName": "vaccineLotNumber", "isProperties": true, "searchType": "text"}, {"fieldName": "veterinarianName", "isProperties": true, "searchType": "text"}, {"fieldName": "basicInfoApproved", "isProperties": true, "searchType": "boolean"}, {"fieldName": "dogSecondaryColor", "isProperties": true, "searchType": "text"}, {"fieldName": "insuranceApproved", "isProperties": true, "searchType": "boolean"}, {"fieldName": "vaccineDatesExempt", "isProperties": true, "searchType": "boolean"}, {"fieldName": "dogSpayedOrNeutered", "isProperties": true, "searchType": "boolean"}, {"fieldName": "vaccineAdministeredDate", "isProperties": true, "searchType": "date_range"}, {"fieldName": "vaccineLotExpirationDate", "isProperties": true, "searchType": "date_range"}, {"fieldName": "physicalCharacteristicsApproved", "isProperties": true, "searchType": "boolean"}, {"fieldName": "dogImpoundmentNotified", "isProperties": true, "searchType": "boolean", "coalesce": true, "coalesceDefaultValue": false}]}