--drop function determineLicenseActivityDate;
create or replace function license.determineLicenseActivityDate(
    IN v_license_uuid uuid
)
RETURNS TABLE(
    valid_from_date TIMESTAMP,
    valid_to_date TIMESTAMP
)
LANGUAGE plpgsql
AS $$
declare
	v_license record;
    v_latest_activity record;
	r_valid_to_date TIMESTAMP;
	r_valid_from_date TIMESTAMP;
begin
	-- lookup the license
	select 
		l.*
	into v_license
	from license.license l
	where l.license_uuid = v_license_uuid;

    if v_license is null then
        raise exception 'License with UUID % not found', v_license_uuid;
    end if;

	-- get the latest activity
	select
		la.*
	into v_latest_activity
	from license.license_activity la 
	where la.license_id = v_license.license_id
	and la.valid_to_date is not null
	order by la.valid_to_date desc
	limit 1;

	if v_latest_activity is null or v_latest_activity.license_activity_id is null then
		if v_license.valid_to_date is null then
			r_valid_from_date:= license.start_of_day(now());
		ELSE
			r_valid_from_date:= v_license.valid_to_date;
		end if;
	ELSE
		r_valid_from_date:= v_latest_activity.valid_to_date;
	end if;
	--check if the r_valid_from_date is more than 1 year in the past we must bring up to date
	if r_valid_from_date < license.start_of_day(now()) - interval '1 year' then
		r_valid_from_date:= license.start_of_day(now());
	end if;

	r_valid_from_date:= license.start_of_day(r_valid_from_date);

	-- determine the valid from date
	r_valid_to_date:= license.end_of_day(license.end_of_month_plus_one_year(r_valid_from_date));

	return query
	select
		r_valid_from_date as valid_from_date,
		r_valid_to_date as valid_to_date;
END;
$$;


select * from determineLicenseActivityDate('1f180691-7e6f-4cdb-8aa3-02efdc95f552');
