[{"feeName": "Dog License Base Fee", "payableId": 0, "amount": 15, "operation": "FLAT", "key": "DL-M-LICENSE"}, {"feeName": "Schenectady Fee (Enumeration)", "payableId": 0, "amount": 5, "operation": "FLAT", "key": "DL-M-DATA"}, {"feeName": "Schenectady Unaltered Fee", "payableId": 0, "amount": 5, "operation": "FLAT", "key": "DL-M-UNALT"}, {"feeName": "State Surcharge - Unaltered", "payableId": 1, "amount": 3, "operation": "FLAT", "key": "DL-S-UNALT"}, {"feeName": "State Surcharge - Altered", "payableId": 1, "amount": 1, "operation": "FLAT", "key": "DL-S-ALT"}, {"feeName": "Senior Citizen Discount", "payableId": 0, "amount": -15, "operation": "FLAT", "key": "DL-M-SENIOR"}, {"feeName": "License Fee Exemption", "payableId": 0, "amount": -12.5, "operation": "FLAT", "key": "DL-M-EXEMPT"}, {"feeName": "License Fee Exemption", "payableId": 0, "amount": -1, "operation": "FLAT", "key": "DL-M-EXEMPTSTATEALT"}, {"feeName": "License Fee Exemption", "payableId": 0, "amount": -3, "operation": "FLAT", "key": "DL-M-EXEMPTSTATEUNALT"}, {"feeName": "Schenectady Unaltered Fee exemption", "payableId": 0, "amount": -5, "operation": "FLAT", "key": "DL-M-EXEMPTUNALT"}, {"feeName": "Purebred License Base Fee", "payableId": 0, "amount": 40, "operation": "FLAT", "key": "DLP-M-LICENSE"}, {"feeName": "Purebred Additional Dog", "payableId": 0, "amount": 0, "operation": "FLAT", "key": "DLP-M-DOG"}, {"feeName": "State Surcharge - Unaltered", "payableId": 0, "amount": 3, "operation": "FLAT", "key": "DLP-S-UNALT"}, {"feeName": "State Surcharge - Altered", "payableId": 0, "amount": 1, "operation": "FLAT", "key": "DLP-S-ALT"}, {"feeName": "ClerKXpress Transaction Fee", "payableId": 2, "amount": 2.5, "operation": "FLAT", "key": "PR-CX-LICENSEFEE"}, {"feeName": "Online Payment Processor", "payableId": 3, "amount": 3, "operation": "PERCENTAGE", "key": "PAY-PP-ONLINE"}, {"feeName": "Dangerous Dog", "payableId": 3, "amount": 0, "operation": "MANUAL", "key": "DLD-M-LICENSE"}, {"feeName": "Dog Redemption", "payableId": 3, "amount": 0, "operation": "MANUAL", "key": "E-M-DOG-REDEMPTION"}, {"feeName": "Replacement Dog Tag", "payableId": 3, "amount": 3.0, "operation": "FLAT", "key": "STORE-M-DOGTAG"}, {"feeName": "Violation", "payableId": 3, "amount": 0, "operation": "FLAT", "key": "PEN-M-VIOLATION"}, {"feeName": "Civil Penalty", "payableId": 3, "amount": 0, "operation": "FLAT", "key": "PEN-M-CIVILPENALTY"}, {"key": "CUSTOM-M-FEE", "feeName": "Custom Fee", "amount": 0, "operation": "MANUAL"}, {"key": "CUSTOM-M-DISCOUNT", "feeName": "Custom Discount", "amount": 0, "operation": "MANUAL"}, {"key": "DOG-M-IMPOUNDMENT-BASE", "feeName": "Dog Impoundment Fee", "operation": "MANUAL", "amount": 80, "entityTypes": ["dog"]}, {"key": "DOG-M-IMPOUNDMENT-RECURRING", "feeName": "Dog Impoundment Fee", "operation": "MANUAL", "amount": 0, "recurringAmount": 80, "startDate": {"type": "relative", "date": "today", "offset": 0, "offsetBy": "days"}, "endDate": {"type": "relative", "date": "today", "offset": 5, "offsetBy": "days"}, "cronExpression": "0 0 0 * * *", "entityTypes": ["dog"]}, {"feeCode": "DOG-M-OTHER", "amount": 100, "operation": "MANUAL", "entityTypes": ["dog"]}]