--drop function if exists fn_dogLicenseFeeCalculation(uuid);
create or replace function license.fn_dogLicenseFeeCalculation(
	in i_license_entity_id uuid
)
returns table (
	fee_type text,
	fee_code text,
	fee_amount NUMERIC(20, 10)
)
LANGUAGE plpgsql
AS $$
declare
	v_license record;
	v_dog_properties jsonb;
	v_owner_properties jsonb;

	v_is_spayed_or_neutered boolean := false;
	v_is_senior boolean := false;
	v_is_exempt boolean := false;
BEGIN
    select 
    	l.*
	into v_license
	from license.view_license l
	where l.license_type_code = 'dogLicense'
	and l.license_uuid = i_license_entity_id;

	if v_license is null then
        raise exception 'License with UUID % not found', i_license_entity_id;
    end if;

	SELECT
		pdog.properties
	INTO v_dog_properties	
	FROM license.association aDog

	LEFT JOIN license.view_participant pdog
		on pdog.participant_id = aDog.child_id
	
    WHERE aDog.parent_association_type = 'LICENSE'
    AND aDog.child_association_type = 'PARTICIPANT'
    AND aDog.parent_id = v_license.license_id
	and pdog.group_name ILIKE 'Dog';

	if v_dog_properties is null then
		raise exception 'Dog not found for license with UUID %', i_license_entity_id;
	end if;

	select 
		powner.properties
	into v_owner_properties
	from license.association aOwner

	LEFT JOIN license.view_participant powner
		on powner.participant_id = aOwner.child_id

	where aOwner.parent_association_type = 'LICENSE'
	and aOwner.child_association_type = 'PARTICIPANT'
	and aOwner.parent_id = v_license.license_id
	and powner.group_name ILIKE 'Individual';

	if v_owner_properties is null then
		raise exception 'Owner not found for license with UUID %', i_license_entity_id;
	end if;

	if COALESCE(v_dog_properties->>'dogSpayedOrNeutered','no') ILIKE 'yes' then
		v_is_spayed_or_neutered := true;
	end if;

	if (v_owner_properties->>'dateOfBirth')::date <= (now() - interval '65 years')::date then
		v_is_senior := true;
	end if;

	if COALESCE(v_dog_properties->>'licenseExempt','false') ILIKE 'true' then
		v_is_exempt := true;
	end if;


	-- add the DL-M-LICENSE and DL-M-DATA fees as a base
	RETURN QUERY
    select 
    	'calculated' as fee_type,
    	'DL-M-LICENSE' as fee_code,
		0.0 as fee_amount
	UNION all
    select 
    	'calculated' as fee_type,
    	'DL-M-DATA' as fee_code,
		0.0 as fee_amount;

	-- if the dog is spayed or neutered, add the DL-S-ALT fee
	-- if the dog is not spayed or neutered, add the DL-M-UNALT and DL-S-UNALT fees
	if v_is_spayed_or_neutered then
		RETURN QUERY
	    select 
	    	'calculated' as fee_type,
	    	'DL-S-ALT' as fee_code,
			0.0 as fee_amount;
	else
		RETURN QUERY
	    select 
	    	'calculated' as fee_type,
	    	'DL-M-UNALT' as fee_code,
			0.0 as fee_amount
		union all
	    select 
	    	'calculated' as fee_type,
	    	'DL-S-UNALT' as fee_code,
			0.0 as fee_amount;
	end if;

	-- if the owner is a senior, add the DL-M-SENIOR fee
	-- determine if the owner is senior from the owner's dateOfBirth
	--if v_is_senior and not v_is_exempt then
	if v_is_senior then
		RETURN QUERY
	    select 
	    	'calculated' as fee_type,
	    	'DL-M-SENIOR' as fee_code,
			0.0 as fee_amount;
	end if;

	-- -- if the license is exempt, add the DL-M-EXEMPT fee
	-- if v_is_exempt then
	-- 	RETURN QUERY
	--     select 
	--     	'calculated' as fee_type,
	--     	'DL-M-EXEMPT' as fee_code,
	-- 		0.0 as fee_amount;
	-- end if;

	-- -- if license is exempt and not spayed or neutered, add the DL-M-EXEMPTUNALT and DL-M-EXEMPTSTATEUNALT fee
	-- if v_is_exempt and not v_is_spayed_or_neutered then
	-- 	RETURN QUERY
	--     select 
	--     	'calculated' as fee_type,
	--     	'DL-M-EXEMPTUNALT' as fee_code,
	-- 		0.0 as fee_amount
	-- 	union all
	--     select 
	--     	'calculated' as fee_type,
	--     	'DL-M-EXEMPTSTATEUNALT' as fee_code,
	-- 		0.0 as fee_amount;
	-- end if;

	-- -- if license is exempt and spayed or neutered, add the DL-M-EXEMPTSTATEALT fee
	-- if v_is_exempt and v_is_spayed_or_neutered then
	-- 	RETURN QUERY
	--     select 
	--     	'calculated' as fee_type,
	--     	'DL-M-EXEMPTSTATEALT' as fee_code,
	-- 		0.0 as fee_amount;
	-- end if;
END;
$$;

--select * from fn_dogLicenseFeeCalculation('e53d8676-70d5-41d6-a338-74d66e8efd59');

update license_type lt 
set fee_config_function = 'fn_dogLicenseFeeCalculation'
where lt.code = 'dogLicense';



-- -- Schenectady Fee (Enumeration)
-- update calculation.fee
-- set amount = 5
-- where key = 'DL-M-DATA';

-- -- Dog License Base Fee
-- update calculation.fee
-- set amount = 15
-- where key = 'DL-M-LICENSE';

-- -- Purebred License Base Fee
-- update calculation.fee
-- set amount = 50
-- where key = 'DLP-M-LICENSE';

-- update calculation.fee
-- set amount = -15
-- where key = 'DL-M-SENIOR';

-- -- Senior Citizen Discount
-- update calculation.fee
-- set amount = -50
-- where key = 'DLP-M-SENIOR';

-- update calculation.fee
-- set amount = 0
-- where key ILIKE '%exempt%';

-- UPDATE calculation.fee
-- set amount = '5.00'
-- where key = 'STORE-M-DOGTAG'