CREATE OR REPLACE FUNCTION license.fn_determinePurebredDogLicenseDuration(
    in i_license_entity_id uuid
)
RETURNS table(
    duration int
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_license record;
    v_dog_properties jsonb;

    v_vaccine_expiration_date date;
    v_license_expiration_date date;
    v_is_vaccine_exempt boolean;
    v_max_renewal_years int := 3;
    v_current_date date := CURRENT_DATE;
    v_future_current_date date;
    v_max_vaccine_expiration_date date;
    v_whichever_is_less date;
    v_years_until_exp int := 0;
BEGIN
    -- Fetch license information
    SELECT l.*
    INTO v_license
    FROM license.view_license l
    WHERE l.license_type_code = 'purebredDogLicense'
    AND l.license_uuid = i_license_entity_id;

    IF v_license IS NULL THEN
        RAISE EXCEPTION 'License with UUID % not found', i_license_entity_id;
    END IF;

    -- Fetch dog properties
    SELECT pdog.properties
    INTO v_dog_properties
    FROM license.association aDog
    LEFT JOIN license.view_participant pdog
        ON pdog.participant_id = aDog.child_id
    WHERE aDog.parent_association_type = 'LICENSE'
    AND aDog.child_association_type = 'PARTICIPANT'
    AND aDog.parent_id = v_license.license_id
    AND pdog.group_name ILIKE 'Dog'
    order by pdog.properties->>'vaccineDueDate' ASC
    LIMIT 1;


    IF v_dog_properties IS NULL THEN
        RAISE EXCEPTION 'Dog not found for license with UUID %', i_license_entity_id;
    END IF;

    -- Fetch vaccine information
    v_vaccine_expiration_date := (v_dog_properties->>'vaccineDueDate')::date;
    v_is_vaccine_exempt := COALESCE(v_dog_properties->>'vaccineDatesExempt','false') = 'true';
    v_license_expiration_date := COALESCE(v_license.valid_to_date, now());

    -- Calculate future current date (3 years from the current month end)
    v_future_current_date := (license.addYears(license.end_of_month(v_current_date), v_max_renewal_years))::date;

    -- Check if vaccine exempt, calculate based on license expiration
    IF v_is_vaccine_exempt THEN
        v_years_until_exp := GREATEST(
            LEAST(
                v_max_renewal_years, 
                DATE_PART('year', AGE(v_future_current_date, v_license_expiration_date))
            ), 
            0
        );
        RETURN QUERY SELECT v_years_until_exp;
    END IF;

    -- If vaccine expiration exists, calculate based on it
    IF v_vaccine_expiration_date IS NOT NULL THEN
        v_max_vaccine_expiration_date := (license.end_of_month(license.addMonths(v_vaccine_expiration_date, 11)))::date;

        -- Choose the lesser of max vaccine expiration date or future current date
        v_whichever_is_less := LEAST(v_max_vaccine_expiration_date, v_future_current_date);

        -- Calculate years until expiration
        v_years_until_exp := GREATEST(
            LEAST(v_max_renewal_years, 
                DATE_PART('year', AGE(v_whichever_is_less, v_license_expiration_date))
            ), 
            0
        );
    ELSE
        -- No vaccine info, default to 0
        v_years_until_exp := 0;
    END IF;

    RETURN QUERY SELECT v_years_until_exp;

END;
$$;

--select * from license.fn_determinePurebredDogLicenseDuration('3738c18c-f4a6-4db8-9441-b21327b7e188');
-- with multiple dogs
--select * from license.fn_determinePurebredDogLicenseDuration('edec0f54-8ef2-4f76-a36f-4f05b8f72fe1');

update license_type lt 
set duration_function = 'fn_determinePurebredDogLicenseDuration'
where lt.code = 'purebredDogLicense';