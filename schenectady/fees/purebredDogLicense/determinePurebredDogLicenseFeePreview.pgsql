drop FUNCTION if exists license.determinePurebredDogLicenseFeePreview;
CREATE OR REPLACE FUNCTION license.determinePurebredDogLicenseFeePreview(
    in i_license_entity_id uuid
)
RETURNS TABLE (
    fee_type text,
    fee_code text,
    fee_amount NUMERIC(20, 10),
    label text,
    duration int
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_max_duration int := 0;
    v_i int := 1;
    v_j int := 1;
BEGIN
    SELECT d.duration
    INTO v_max_duration
    FROM license.fn_determinePurebredDogLicenseDuration(i_license_entity_id) d;
    
    FOR v_i IN 1..v_max_duration LOOP
        FOR v_j in 1..v_i LOOP
            RETURN QUERY
            SELECT 
                f.fee_type,
                f.fee_code,
                f.fee_amount,
                v_i || ' year' || CASE WHEN v_i > 1 THEN 's' ELSE '' END AS label,
                v_i AS duration
            FROM 
                license.fn_purebredDogLicenseFeeCalculation(i_license_entity_id) f;
        END LOOP;
    END LOOP;
END $$;


--select * from license.determinePurebredDogLicenseFeePreview('e53d8676-70d5-41d6-a338-74d66e8efd59');

update license_type lt 
set fee_preview_function = 'determinePurebredDogLicenseFeePreview'
where lt.code = 'purebredDogLicense';