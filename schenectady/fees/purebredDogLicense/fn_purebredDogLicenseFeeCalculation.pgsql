--drop function if exists fn_purebredDogLicenseFeeCalculation(uuid);
create or replace function license.fn_purebredDogLicenseFeeCalculation(
	in i_license_entity_id uuid
)
returns table (
	fee_type text,
	fee_code text,
	fee_amount NUMERIC(20, 10)
)
LANGUAGE plpgsql
AS $$
declare
	v_license record;
BEGIN
    select 
    	l.*
	into v_license
	from license.view_license l
	where l.license_type_code = 'purebredDogLicense'
	and l.license_uuid = i_license_entity_id;

	if v_license is null then
        raise exception 'License with UUID % not found', i_license_entity_id;
    end if;

	-- add the DL-M-LICENSE and DL-M-DATA fees as a base
	RETURN QUERY
    select 
    	'calculated' as fee_type,
    	'DLP-M-LICENSE' as fee_code,
		0.0 as fee_amount
	UNION all
    select 
    	'calculated' as fee_type,
    	'DLP-M-DOG' as fee_code,
		0.0 as fee_amount
    UNION all
    SELECT
		'calculated' as fee_type,
        case 
            when COALESCE(pdog.properties->>'dogSpayedOrNeutered', 'no') ILIKE 'yes' then 'DLP-S-ALT'
            else 'DLP-S-UNALT'
        end as fee_code,
        0.0 as fee_amount	
	FROM license.association aDog

	LEFT JOIN license.view_participant pdog
		on pdog.participant_id = aDog.child_id
	
    WHERE aDog.parent_association_type = 'LICENSE'
    AND aDog.child_association_type = 'PARTICIPANT'
    AND aDog.parent_id = v_license.license_id
	and pdog.group_name ILIKE 'Dog';

END;
$$;

--select * from fn_purebredDogLicenseFeeCalculation('e53d8676-70d5-41d6-a338-74d66e8efd59');

update license_type lt 
set fee_config_function = 'fn_purebredDogLicenseFeeCalculation'
where lt.code = 'purebredDogLicense';