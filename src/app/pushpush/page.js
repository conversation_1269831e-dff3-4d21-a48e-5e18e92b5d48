'use client';

import { useEffect, useState, useRef } from 'react';
import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';

export default function ChatPushPage() {
  const [messages, setMessages] = useState([]);
  const stompClientRef = useRef(null);

  useEffect(() => {
    const token = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJPMV9KbjM1X044ZUdvT3dxV0NuM1dJbTNDbXhnMDhJWkppTHB5U0lfS3pNIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O8VcBA-uLF4NKSeHQOq01vT9XWYDYZiFsegUmvc4fa4EK4LUgzunpfjgpXSCZ7DK3QgM8f-eyEe9sMalWgTgzOWrOWD7i5NFhg3nnz3oIAHenA4hNun5oq1SdHGaUkIlKd6PyiOxMwxxPoAQgsHRamGjSkuLajT0kRPCVzBhxQQG2tmiEsZc3zQ-RpJrk7ZYE2PLIZYQyqMy_RsHQjo4YqpbLVGYf44k67VdmQaxGVC8sh78ommMBa5uYcmmHJdL1_IdgpsGmjzxQyEP7B6QWUPayTAMWUlZug7dg6liRWvmVglwVYNeJuJTUjLf5y69MX3W7u_3-PDRBl43Pmv7hg";

    const socket = new SockJS('http://localhost:9090/api/wb/ws');

    const stompClient = new Client({
      webSocketFactory: () => socket,
      connectHeaders: {
        Authorization: `Bearer ${token}`,
      },
      reconnectDelay: 5000,
      onConnect: () => {
        console.log('[Connected] ✅ Subscribing to /topic/server-push...');
        stompClient.subscribe('/topic/server-push', (msg) => {
          setMessages((prev) => [...prev, `🔔 ${msg.body}`]);
        });
      },
      onStompError: (frame) => {
        console.error('❌ STOMP error:', frame.headers['message']);
      },
      debug: (str) => console.log('[STOMP Debug]', str),
    });

    stompClient.activate();
    stompClientRef.current = stompClient;

    return () => {
      stompClient.deactivate();
    };
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial' }}>
      <h2>🔁 Server Push Notifications</h2>
      <div>
        <p>Listening to <code>/topic/server-push</code>...</p>
      </div>
      <ul style={{ marginTop: '20px', paddingLeft: '20px' }}>
        {messages.length === 0 && <li>No messages received yet.</li>}
        {messages.map((msg, idx) => (
          <li key={idx} style={{ marginBottom: '8px' }}>{msg}</li>
        ))}
      </ul>
    </div>
  );
}
