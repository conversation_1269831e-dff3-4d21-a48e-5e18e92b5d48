'use client';

import { useEffect, useState, useRef } from 'react';
import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';

export default function PrivateChat() {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [token, setToken] = useState('');
  const [recipient, setRecipient] = useState('');
  const [connected, setConnected] = useState(false);

  const stompClientRef = useRef(null);

  const connect = () => {
    if (!token) {
      alert('Please enter a valid token.');
      return;
    }

    const socket =  new SockJS('http://localhost:9013/api/support/ws');

    const stompClient = new Client({
      webSocketFactory: () => socket,
      connectHeaders: {
          Authorization: 'Bearer '+token,  // 👈 Send token in connectHeaders
        },
      debug: (str) => console.log('[STOMP]', str),
      reconnectDelay: 5000,
      onConnect: () => {
        console.log('[Connected]');
        setConnected(true);
        stompClient.subscribe('/user/queue/messages', (message) => {
          console.log('[Received]', message.body);
          setMessages((prev) => [...prev, `📩 ${message.body}`]);
        });
      },
      onStompError: (frame) => {
        console.error('[STOMP Error]', frame.headers['message'], frame.body);
        alert('STOMP error occurred');
      },
    });

    stompClient.activate();
    stompClientRef.current = stompClient;
  };

  const sendPrivateMessage = () => {
    if (!connected) {
      alert('Please connect first.');
      return;
    }
    if (input.trim() && stompClientRef.current?.connected) {
      stompClientRef.current.publish({
        destination: '/app/private-message',
        body: JSON.stringify({
          to: recipient,
          content: input,
        }),
      });
      setMessages((prev) => [...prev, `🧑‍💻 You: ${input}`]);
      setInput('');
    }
  };

  return (
    <div className="max-w-xl mx-auto mt-10 p-6 bg-white shadow-md rounded-lg">
      <h1 className="text-2xl font-bold text-center text-gray-800 mb-6">🔐 Private Chat</h1>

      <div className="space-y-4">
        <input
          className="w-full px-4 py-2 border rounded-md text-gray-800 placeholder-gray-500"
          placeholder="🔑 Enter Token"
          value={token}
          onChange={(e) => setToken(e.target.value)}
        />

        <input
          className="w-full px-4 py-2 border rounded-md text-gray-800 placeholder-gray-500"
          placeholder="✉️ Recipient Email"
          value={recipient}
          onChange={(e) => setRecipient(e.target.value)}
        />

        <button
          className={`w-full py-2 px-4 rounded-md text-white font-semibold ${
            connected ? 'bg-green-500 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600'
          }`}
          onClick={connect}
          disabled={connected}
        >
          {connected ? 'Connected ✅' : 'Connect'}
        </button>
      </div>

      {connected && (
        <div className="mt-6">
          <div className="h-60 overflow-y-auto bg-gray-50 border rounded-md p-4 space-y-2 mb-4">
            {messages.map((msg, idx) => (
              <div
                key={idx}
                className="bg-blue-100 text-blue-900 px-3 py-1 rounded-md text-sm"
              >
                {msg}
              </div>
            ))}
          </div>

          <div className="flex gap-2">
            <input
              className="flex-1 px-4 py-2 border rounded-md text-gray-800 placeholder-gray-500"
              placeholder="💬 Type a message..."
              value={input}
              onChange={(e) => setInput(e.target.value)}
            />
            <button
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
              onClick={sendPrivateMessage}
            >
              Send
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
