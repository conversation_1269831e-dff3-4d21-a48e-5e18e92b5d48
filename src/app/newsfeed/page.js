'use client';

import { useEffect, useState, useRef } from 'react';
import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';

export default function NewsFeed() {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const stompClientRef = useRef(null);

  useEffect(() => {
    const token = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJPMV9KbjM1X044ZUdvT3dxV0NuM1dJbTNDbXhnMDhJWkppTHB5U0lfS3pNIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O8VcBA-uLF4NKSeHQOq01vT9XWYDYZiFsegUmvc4fa4EK4LUgzunpfjgpXSCZ7DK3QgM8f-eyEe9sMalWgTgzOWrOWD7i5NFhg3nnz3oIAHenA4hNun5oq1SdHGaUkIlKd6PyiOxMwxxPoAQgsHRamGjSkuLajT0kRPCVzBhxQQG2tmiEsZc3zQ-RpJrk7ZYE2PLIZYQyqMy_RsHQjo4YqpbLVGYf44k67VdmQaxGVC8sh78ommMBa5uYcmmHJdL1_IdgpsGmjzxQyEP7B6QWUPayTAMWUlZug7dg6liRWvmVglwVYNeJuJTUjLf5y69MX3W7u_3-PDRBl43Pmv7hg";
    const socket = new SockJS('http://localhost:9090/api/wb/ws');

    const stompClient = new Client({
      webSocketFactory: () => socket,
      connectHeaders: {
        Authorization: `Bearer ${token}`,
      },
      onConnect: () => {
        console.log('[Connected]');
        // Subscribe to the news topic
        stompClient.subscribe('/topic/news', (message) => {
          setMessages((prev) => [...prev, `📰 ${message.body}`]);
        });
      },
      reconnectDelay: 5000,
      debug: (str) => console.log(str),
    });

    stompClient.activate();
    stompClientRef.current = stompClient;

    return () => {
      stompClient.deactivate();
    };
  }, []);

  const publishNews = () => {
    if (input.trim() && stompClientRef.current?.connected) {
      stompClientRef.current.publish({
        destination: '/app/news',
        body: input,
      });
      setInput('');
    }
  };

  return (
    <div style={{ padding: 20 }}>
      <h2>🗞️ News Feed (Pub/Sub)</h2>
      <input
        value={input}
        onChange={(e) => setInput(e.target.value)}
        placeholder="Type news to publish..."
      />
      <button onClick={publishNews}>Publish</button>
      <ul>
        {messages.map((msg, idx) => (
          <li key={idx}>{msg}</li>
        ))}
      </ul>
    </div>
  );
}
