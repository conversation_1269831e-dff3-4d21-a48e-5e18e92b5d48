'use client';

import { useEffect, useState, useRef } from 'react';
import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';

export default function ChatPage() {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const stompClientRef = useRef(null);

  useEffect(() => {
    const token = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJPMV9KbjM1X044ZUdvT3dxV0NuM1dJbTNDbXhnMDhJWkppTHB5U0lfS3pNIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dhbuTWKhS3YIncNytXBvpdmphCfOK5ymoRWV9ZhU4JH97WQkVdACkbrUNg0lWAECmW2Qt9IWzlEX2NMdGQOuS9Pi4GaJnG5cLeotYfWKzFk0dY7nVReC7e8XhD0PEU_cwRv0mjAus9UL_u_qG1P11VsP7vPgl020rQRzM_vinK2zF07vfJidIK2l6GNMY8um9Wo-MDGN66ETrd14MywPAUqG3QekxZpxxY7J7k7nj0UU_wxzQcSydO3JS3KdEQO4VaWiMNQPzDJFB8mfYi7HnhEaOyrS1qa155zRmUaLbJ7UBBeY4M7ImxxnVsFKRRgFOTDHRiJ0KkBqRV4ZumLyqQ";

    const socket = new SockJS('http://localhost:9090/api/wb/ws?token='+token);
    const stompClient = new Client({
      webSocketFactory: () => socket,
      reconnectDelay: 5000,
      onConnect: () => {
        console.log('[Connected]');
        stompClient.subscribe('/topic/messages', (msg) => {
          setMessages((prev) => [...prev, msg.body]);
        });
      },
      onStompError: (frame) => {
        console.error('STOMP error:', frame.headers['message']);
      },
      debug: (str) => console.log(str),
    });

    stompClient.activate();
    stompClientRef.current = stompClient;

    return () => {
      stompClient.deactivate();
    };
  }, []);

  const sendMessage = () => {
    if (input.trim() && stompClientRef.current?.connected) {
      stompClientRef.current.publish({
        destination: '/app/chat',
        body: input,
      });
      setInput('');
    }
  };

  return (
    <div style={{ padding: 20 }}>
      <h2>WebSocket Chat</h2>
      <input
        value={input}
        onChange={(e) => setInput(e.target.value)}
        placeholder="Type a message..."
      />
      <button onClick={sendMessage}>Send</button>
      <ul>
        {messages.map((msg, idx) => (
          <li key={idx}>{msg}</li>
        ))}
      </ul>
    </div>
  );
}
