import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import FeesSection from '../FeesSection';
import { useMyCart } from '@/hooks/useMyCart';
import { useMyProfile } from '@/hooks/providers/useMyProfile';
import { useGetLicenseFeeTable, useGetResidentLicenseFeeTable } from '@/hooks/api/useLicense';

// Mock the hooks
jest.mock('@/hooks/useMyCart');
jest.mock('@/hooks/providers/useMyProfile');
jest.mock('@/hooks/api/useLicense');
jest.mock('next/navigation', () => ({
  useParams: () => ({ entityId: 'test-entity-id' })
}));

const mockUseMyCart = useMyCart as jest.MockedFunction<typeof useMyCart>;
const mockUseMyProfile = useMyProfile as jest.MockedFunction<typeof useMyProfile>;
const mockUseGetLicenseFeeTable = useGetLicenseFeeTable as jest.MockedFunction<typeof useGetLicenseFeeTable>;
const mockUseGetResidentLicenseFeeTable = useGetResidentLicenseFeeTable as jest.MockedFunction<typeof useGetResidentLicenseFeeTable>;

const mockCartItem = {
  cartItemId: 1,
  itemId: 'test-entity-id',
  itemType: 'license',
  primaryDisplay: 'Test License',
  secondaryDisplay: 'Test Description',
  fees: [],
  subtotal: 100,
  discountedItems: [],
  discount: 0,
  total: 100,
  licenseActivityFeeIds: []
};

const mockCartSummary = {
  cartId: 'test-cart-id',
  userId: 'test-user-id',
  cartStatus: 'active',
  createdDate: '2023-01-01',
  items: [mockCartItem],
  total: 100,
  subtotal: 100,
  summary: [],
  hasAdditionalFee: false
};

const mockFees = {
  feeCode: 'TEST_FEE',
  processedDate: '2023-01-01',
  validFrom: '2023-01-01',
  validTo: '2023-12-31',
  totalAmount: 100,
  totalPaidAmount: 100,
  totalDiscountAmount: 0,
  totalSubtotal: 100,
  fees: [
    {
      feeCode: 'TEST_FEE_1',
      feeStatus: 'paid',
      feeAmount: 50,
      feePaidDate: '2023-01-01'
    }
  ]
};

const mockFeeTable = [
  {
    key: 'TEST_FEE_1',
    feeName: 'Test Fee 1'
  }
];

describe('FeesSection', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });

    // Default mock implementations
    mockUseMyProfile.mockReturnValue({
      hasPermissions: jest.fn().mockReturnValue(false)
    } as any);

    mockUseGetLicenseFeeTable.mockReturnValue({
      data: mockFeeTable,
      isLoading: false,
      isError: false,
      error: null
    } as any);

    mockUseGetResidentLicenseFeeTable.mockReturnValue({
      data: mockFeeTable,
      isLoading: false,
      isError: false,
      error: null
    } as any);
  });

  const renderComponent = (props = {}) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <FeesSection 
          fees={mockFees} 
          refetch={jest.fn()} 
          {...props} 
        />
      </QueryClientProvider>
    );
  };

  it('shows loading state when cart is loading', () => {
    mockUseMyCart.mockReturnValue({
      cartSummary: null,
      cartIsFetching: false,
      cartLoading: true
    } as any);

    renderComponent();
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows updating state when cart is fetching', () => {
    mockUseMyCart.mockReturnValue({
      cartSummary: mockCartSummary,
      cartIsFetching: true,
      cartLoading: false
    } as any);

    renderComponent();
    expect(screen.getByText('Updating...')).toBeInTheDocument();
  });

  it('shows cart item not found when cart item is missing', () => {
    mockUseMyCart.mockReturnValue({
      cartSummary: { ...mockCartSummary, items: [] },
      cartIsFetching: false,
      cartLoading: false
    } as any);

    renderComponent();
    expect(screen.getByText('Cart item not found')).toBeInTheDocument();
  });

  it('renders fees section when cart item is available', async () => {
    mockUseMyCart.mockReturnValue({
      cartSummary: mockCartSummary,
      cartIsFetching: false,
      cartLoading: false
    } as any);

    renderComponent();

    await waitFor(() => {
      expect(screen.getByText(/Fee Processed Date:/)).toBeInTheDocument();
      expect(screen.getByText(/License Dates:/)).toBeInTheDocument();
      expect(screen.getByText('Test Fee 1')).toBeInTheDocument();
    });
  });

  it('applies updating styles when component is updating', async () => {
    mockUseMyCart.mockReturnValue({
      cartSummary: mockCartSummary,
      cartIsFetching: false,
      cartLoading: false
    } as any);

    const { container } = renderComponent();

    await waitFor(() => {
      const feesSectionDiv = container.querySelector('.max-w-2xl');
      expect(feesSectionDiv).toBeInTheDocument();
    });
  });
});
