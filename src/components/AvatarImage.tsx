import { useGetDocumentBlob } from "@/hooks/api/useServices";
import Image, { ImageProps } from "next/image";
import React from "react";
import { Skeleton } from "./ui/skeleton";
import { cn } from "@/lib/utils";
import { Document } from "@/types/DocumentType";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

interface AvatarImageProps extends ImageProps {
  src: string;
  width?: number;
  height?: number;
  alt: string;
  className?: string;
  entityType: string;
}

const AvatarImage = ({
  src,
  width,
  height,
  alt,
  className,
  entityType,
  fill = true,
  ...props
}: AvatarImageProps) => {
  
  if (!src || src === "" || src === "null" || src === "undefined")
    return (
      <DefaultImage
        {...{ src, width, height, alt, className, entityType, fill }}
      />
    );

  return (
    <APIImage {...{ src, width, height, alt, className, entityType, fill }} />
  );
};

export default AvatarImage;

// API Image Comp

const APIImage = ({
  src,
  width,
  height,
  alt,
  className,
  entityType,
  fill = false,
  ...props
}: AvatarImageProps) => {
  const {
    data: avatarBlob,
    isError: isLoading,
    isLoading: isError,
  } = useGetDocumentBlob(src, true);

  console.log(avatarBlob);
  console.log(src)

  if (isLoading)
    return (
      <Skeleton
        style={{
          width: `${width}px`,
          height: `${height}px`,
        }}
        className={cn(className)}
        {...props}
      />
    );

  if (isError)
    return (
      <div
        style={{
          width: `${width}px`,
          height: `${height}px`,
        }}
        className="relative shrink-0"
      >
        <Image
          src={defaultImgMap[entityType]}
          fill
          sizes={`${width}px`}
          alt={alt}
          className={cn(className)}
          style={{
            objectFit: "cover",
            objectPosition: "center center",
          }}
          {...props}
        />
      </div>
    );

  return (
    <div
      style={{
        width: `${width}px`,
        height: `${height}px`,
      }}
      className="relative shrink-0"
    >
      <Image
        src={URL.createObjectURL(avatarBlob)}
        alt={alt}
        className={cn(className)}
        fill
        sizes={`${width}px`}
        style={{
          objectFit: "cover",
          objectPosition: "center center",
        }}
        {...props}
      />
    </div>
  );
};

const DefaultImage = ({
  src,
  width,
  height,
  alt,
  className,
  entityType,
  fill = false,
  ...props
}: AvatarImageProps) => {
  return (
    <div
      style={{
        width: `${width}px`,
        height: `${height}px`,
      }}
      className="relative shrink-0"
    >
      <Image
        src={defaultImgMap[entityType]}
        fill={true}
        alt={alt}
        className={cn(className)}
        sizes={`${width}px`}
        // height={height}
        // width={width}
        style={{
          objectFit: "cover",
          objectPosition: "center center",
        }}
        {...props}
      />
    </div>
  );
};

// Get Avatar
export const getAvatarBlob = (docs?: Document[]) =>
  docs?.find((doc) => doc.key.toLowerCase() === "avatar")?.documentUuid ?? null;

const defaultImgMap: { [key: string]: string } = {
  individual: "/images/icons/user.png",
  dog: "/images/icons/dog.png",
  company: "/images/icons/organization.png",
  address: "/images/icons/address.png",
  parcel: "/images/icons/address.png",
  license: "/images/icons/license.png",
  default: "/images/icons/user.png",
};
