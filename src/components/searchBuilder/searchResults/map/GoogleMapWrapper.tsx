import React, { ReactElement } from "react";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import { Wrapper, Status } from "@googlemaps/react-wrapper";

const render = (status: Status): ReactElement => {
  console.log(status)
  if (status === Status.FAILURE) return <div>error loading map</div>;
  return (
    <div className="flex flex-col justify-center items-center">
      <LoadingSpinner />
      Loading map
    </div>
  );
};

const GoogleMapWrapper = ({ 
  children 
}:{
  children: React.ReactNode;
}) => {
  return (
    <Wrapper
      apiKey={process.env.NEXT_PUBLIC_MAP_API_KEY ?? ""}
      // version='beta'
      libraries={["marker"]}
      render={render}
    >
      {children}
    </Wrapper>
  );
};

export default GoogleMapWrapper;
