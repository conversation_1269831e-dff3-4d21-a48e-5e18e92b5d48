import { useEffect, useRef } from "react";
import { createRoot } from "react-dom/client";
import { MarkerProps } from "../../types/SearchBuilderTypes";
import Image from "next/image";
import Link from "next/link";

const Marker: React.FC<MarkerProps> = ({ map, position, children }): JSX.Element | null => {
  
  const markerRef = useRef<any>();
  const rootRef = useRef<any>();

  useEffect(() => {
    if (!rootRef.current) {
      const container = document.createElement("div");
      rootRef.current = createRoot(container);
      markerRef.current = new window.google.maps.marker.AdvancedMarkerElement({
        position: position,
        content: container,
      });
      console.log(markerRef)
    } 
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    console.log("rendering")
    rootRef.current?.render(children);
    markerRef.current.position = position;
    markerRef.current.map = map;
  }, [map, position, children]);

  return null;
};

type MarkersProps = {
  map: any;
  data: any
}

const markerColor:{
  [key: string]: string
} = {
  default: "bg-black text-white hover:bg-gray-500 hover:text-white",
  individual: "bg-blue-300 text-blue-950 hover:bg-blue-500 hover:text-white",
  organization: "bg-green-300 text-green-950 hover:bg-green-500 hover:text-white",
  address: "bg-black text-white hover:bg-gray-500 hover:text-white",
  dog: "bg-orange-300 text-orange-950 hover:bg-orange-500 hover:text-white"
}

const entityIcon:{
  [key: string]: string
} = {
  default: "/images/icons/user.png",
  individual: "/images/icons/individual.png",
  organization: "/images/icons/organization.png",
  address: "/images/icons/address.png",
  dog: "/images/icons/dog.png"
}

const GoogleMarkers = ({ map, data }: MarkersProps) => data && data.map((marker:any, key:any) => {

  const position = {
    lat: Number(marker.latitude),
    lng: Number(marker.longitude)
  }


  return (
    <Marker map={map} key={key} position={position}>
      <Link 
        href={`/entity/${marker.entityType}/${marker.entityId}?tab=profile`}
        className={`
          ${markerColor[marker.entityType]} 
          rounded-full h-8 flex justify-center items-center cursor-pointer shadow gap-2 p-1 z-50
          pointer-events-auto
        `}
      >
        <Image
          src={marker.avatarUrl?.length > 0 ? marker.avatarUrl : entityIcon[marker.entityType]}
          alt={marker.primaryDisplay}
          width={24}
          height={24}
          className="rounded-full"
        />
        <p className="text-xs font-bold text-center max-w-[100px] pr-4 line-clamp-2">
          {marker.primaryDisplay}
        </p>
      </Link>
    </Marker>
  )
});


export default GoogleMarkers;
