import React, { useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FiMap, FiList } from 'react-icons/fi'

import SearchResult from "@/components/searchBuilder/searchResults/SearchResult";
import { IconType } from 'react-icons/lib';
import MapResults from './map/MapResults';

type DisplayResult = 'list' | 'map'

type DisplayTypes = { 
  name: DisplayResult,
  Icon: IconType
}

const displayTypes:DisplayTypes[] = [
  {
    name: 'list',
    Icon: FiList
  },
  {
    name: 'map',
    Icon: FiMap
  }
]

const Results = ({ results }: { results: any[] }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [displayType, setDisplayType] = React.useState<DisplayResult>("list");

  useEffect( () => {
    if(containerRef.current) {
      containerRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [displayType])  

  if (results?.length > 0 ) {
    return (
      <div ref={containerRef} className="bg-white m-6 rounded-lg p-6 shadow-lg border border-neutral-200 flex flex-col gap-4 min-h-[calc(100svh-70px)]">
        <div className='flex justify-between items-center '>
          <h1 className="text-2xl">Result(s):</h1>
          <div className='
            flex gap-2 border border-neutral-200 rounded-lg p-1 
          '>
            {displayTypes.map((type, index) =>{
              return(
                <motion.button
                  key={index}
                  className={`p-1 rounded text-neutral-600 text-xl ${displayType === type.name ? 'bg-indigo-500 text-white' : 'hover:bg-neutral-200'}`}
                  onClick={() => setDisplayType(type.name)}
                >
                  <type.Icon />
                </motion.button>
              )
            })}
          </div>
        </div>
        { displayType === 'list' && results.map((result, index) => <SearchResult key={index} result={result} />)} 
        { displayType === 'map' && <MapResults results={results} /> }
      </div>
    );
  }
  return null;
};

export default Results