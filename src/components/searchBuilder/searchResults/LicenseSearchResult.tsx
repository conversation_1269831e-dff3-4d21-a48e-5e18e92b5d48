"use client";
import { useState, useEffect, useRef } from "react";
import { FiCircle } from "react-icons/fi";
import { useRouter } from "next/navigation";
import { HiOutlineMail, HiOutlinePhone } from "react-icons/hi";
import AvatarImage from "@/components/AvatarImage";

type SearchResultProps = {
  result: any;
  onClick: () => void;
  search: string;
};

type Icon = {
  [key: string]: string;
};

const icons: Icon = {
  individual: "/images/icons/user.png",
  dog: "/images/icons/dog.png",
  company: "/images/icons/organization.png",
  address: "/images/icons/address.png",
  parcel: "/images/icons/address.png",
  license: "/images/icons/license.png",
  default: "/images/icons/user.png",
};

const typeToIconMap: any = {
  email: <HiOutlineMail className="mr-1 mt-[1px] inline-block" />,
  phone: <HiOutlinePhone className="mr-1 mt-[1px] inline-block" />,
  active: <FiCircle className="mr-1 inline-block text-green-500" />,
  inactive: <FiCircle className="mr-1 inline-block text-red-500" />,
};

const formatPhoneNumber = (phoneNumber: string) => {
  return phoneNumber.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
};

function isValidImageUrl(url: string) {
  const imageExtensions = /\.(jpeg|jpg|gif|png|bmp|webp)$/i;

  return imageExtensions.test(url);
}

function isValidUrl(url: string) {
  const urlPattern = /^(ftp|http|https):\/\/[^ "]+$/;

  return urlPattern.test(url);
}

function sanitizeInput(input: string): string {
  return input.replace(/[^\w\s]/gi, "");
}

function highlightMatch(text: string, query: string) {
  query = sanitizeInput(query);

  if (!text) return null;

  const index = text?.toLowerCase().indexOf(query.toLowerCase());
  if (index === -1) return text;

  const before = text.substring(0, index);
  const match = text.substring(index, index + query.length);
  const after = text.substring(index + query.length);

  return (
    <>
      {before}
      <span className="bg-yellow-200">{match}</span>
      {after}
    </>
  );
}

const LicenseSearchResult = ({
  result,
  onClick,
  search,
}: SearchResultProps) => {
  const [imageError, setImageError] = useState<boolean>(false);
  const mainDivRef = useRef<HTMLDivElement>(null);

  
  const router = useRouter();

  useEffect(() => {
    // Check if mainDivRef.current is not null before calling focus
    if (mainDivRef.current) {
      // mainDivRef.current.focus();
    }
  }, []);

  if (!result) return null;

  const { primaryDisplay, secondaryDisplay, thirdDisplay, avatarUrl } = result;

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    switch (event.key) {
      case "ArrowDown":
        // Move focus to next LicenseSearchResult item
        event.preventDefault();
        const nextElement = mainDivRef?.current?.nextSibling;
        nextElement && (nextElement as HTMLElement).focus();
        break;
      case "ArrowUp":
        // Move focus to previous LicenseSearchResult item
        event.preventDefault();
        const prevElement = mainDivRef?.current?.previousSibling;
        prevElement && (prevElement as HTMLElement).focus();
        break;
      case "Enter":
        // Navigate to the link
        onClick();
        router.push(
          profilePath(result.entityType, result.entityId)
        );
        break;
      default:
        break;
    }
  };

  const avatar =
    !imageError && isValidUrl(avatarUrl) && isValidImageUrl(avatarUrl)
      ? avatarUrl
      : icons[result.entityType];

  const profilePath = (entityType: string, entityId: string) => {
    switch (entityType) {
      case "individual":
        return `/profile/${entityType}/${entityId}?tab=profile`;
      case "organization":
        return `/profile/${entityType}/${entityId}?tab=profile`;
      case "dog":
        return `/profile/${entityType}/${entityId}?tab=profile`;
      case "address":
        return `/profile/${entityType}/${entityId}?tab=profile`;
      case "parcel":
        return `/profile/${entityType}/${entityId}?tab=profile`;
      case "license":
        return `/a/${entityType}/${entityId}`;
      default:
        return "";
    }
  };

  return (
    <div
      ref={mainDivRef}
      tabIndex={0}
      onKeyDown={handleKeyDown}
      className={`flex cursor-pointer flex-col items-center gap-2 rounded border bg-white hover:bg-gradient-to-br hover:from-blue-100 hover:to-blue-50`}
    >
      <div
        className="flex w-full items-center gap-2 p-2 text-left"
        onClick={() => {
          onClick();
          router.push(profilePath(result.entityType, result.entityId));
        }}
      >
        <AvatarImage
          alt="Avatar"
          entityType={result.entityType}
          src={result.avatarUUID as string}
          onError={(e) => {
            if (!imageError) {
              e.currentTarget.onerror = null;
              e.currentTarget.src = icons["default"];
              setImageError(true);
            }
          }}
          height={70}
          width={70}
          fill
          placeholder="blur"
          blurDataURL={icons["default"]}
          className="rounded object-cover object-center"
          style={{ objectFit: "cover" }}
        />

        <div className="w-full">
          <p className="text-lg font-bold">
            {highlightMatch(primaryDisplay, search)}
          </p>
          <p className="text-sm text-neutral-500">
            {highlightMatch(secondaryDisplay, search)}
          </p>
          {thirdDisplay && (
            <p className="mt-auto flex items-center gap-6 text-base text-neutral-600">
              {thirdDisplay.map((item: any, index: number) => (
                <span
                  key={index}
                  className="mt-1 line-clamp-1 flex items-center justify-center text-xs text-neutral-800"
                >
                  {typeToIconMap[item.type]}
                  {item.type === "phone" &&
                    highlightMatch(formatPhoneNumber(item.value), search)}
                  {item.type !== "phone" && highlightMatch(item.value, search)}
                </span>
              ))}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default LicenseSearchResult;
