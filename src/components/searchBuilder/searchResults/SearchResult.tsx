"use client";
import { useState } from "react";
import Image from "next/image";
import { AiFillPlusCircle } from "react-icons/ai";
import { FiCircle, FiMoreVertical } from "react-icons/fi";
import { useRouter } from "next/navigation";
import AddLicense from "@/components/license/AddLicense";
import { HiOutlineMail, HiOutlinePhone } from "react-icons/hi";
import { useParams } from "next/navigation";
import AvatarImage, { getAvatarBlob } from "@/components/AvatarImage";

type SearchResultProps = {
  result: any;
};

const SearchResult = ({ result }: SearchResultProps) => {
  const [showIcons, setShowIcons] = useState<boolean>(false);
  const [imageError, setImageError] = useState<boolean>(false);

  
  const router = useRouter();

  if (!result) return null;

  const { primaryDisplay, secondaryDisplay, thirdDisplay, avatarUrl } = result;

  type Icon = {
    [key: string]: string;
  };

  const icons: Icon = {
    individual: "/images/icons/user.png",
    dog: "/images/icons/dog.png",
    company: "/images/icons/organization.png",
    address: "/images/icons/address.png",
    default: "/images/icons/user.png",
  };

  const typeToIconMap: any = {
    email: <HiOutlineMail className="mr-1 mt-[1px] inline-block" />,
    phone: <HiOutlinePhone className="mr-1 mt-[1px] inline-block" />,
    active: <FiCircle className="mr-1 inline-block text-green-500" />,
    inactive: <FiCircle className="mr-1 inline-block text-red-500" />,
  };

  const formatPhoneNumber = (phoneNumber: string) => {
    return phoneNumber.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
  };

  function isValidImageUrl(url: string) {
    // Regular expression to check if the URL points to an image file
    const imageExtensions = /\.(jpeg|jpg|gif|png|bmp|webp)$/i;

    return imageExtensions.test(url);
  }

  function isValidUrl(url: string) {
    // Regular expression to check if the URL format is valid
    const urlPattern = /^(ftp|http|https):\/\/[^ "]+$/;

    return urlPattern.test(url);
  }

  // const avatar =
  //   !imageError && isValidUrl(avatarUrl) && isValidImageUrl(avatarUrl)
  //     ? avatarUrl
  //     : icons[result.entityType];

  console.log(result);

  return (
    <div
      className={`flex cursor-pointer flex-col items-center gap-2 rounded border hover:bg-gradient-to-br hover:from-blue-100 hover:to-blue-50`}
    >
      <div
        className="flex w-full items-center gap-2 p-2 text-left"
        onClick={() => {
          router.push(
            `/profile/${result.entityType}/${result.entityId}?tab=profile`,
          );
        }}
      >
        <AvatarImage
          entityType={result.entityType as string}
          src={result.avatarUUID}
          alt="Avatar"
          className="mr-4 rounded"
          width={70}
          height={70}
          onError={(e) => {
            if (!imageError) {
              e.currentTarget.onerror = null;
              e.currentTarget.src = icons["default"];
              setImageError(true); // Set the error state
            }
          }}
          placeholder="blur"
          blurDataURL={icons["default"]}
        />

        <div className="w-full">
          <p className="text-lg font-bold">{primaryDisplay}</p>
          <p className="text-sm text-neutral-500">{secondaryDisplay}</p>
          {thirdDisplay && (
            <p className="mt-auto flex items-center gap-6 text-base text-neutral-600">
              {thirdDisplay.map((item: any, index: number) => (
                <span
                  key={index}
                  className="mt-1 line-clamp-1 flex items-center justify-center text-xs text-neutral-800"
                >
                  {typeToIconMap[item.type]}
                  {item.type === "phone" && formatPhoneNumber(item.value)}
                  {item.type !== "phone" && item.value}
                </span>
              ))}
            </p>
          )}
        </div>

        <div className="relative h-auto w-auto xl:hidden">
          <button
            className="relative flex h-8 w-8 items-center justify-center rounded border hover:bg-blue-200"
            onClick={(e) => {
              e.stopPropagation();
              setShowIcons(!showIcons);
            }}
          >
            <FiMoreVertical className="text-2xl" />
            {showIcons && <Icons entityId={result.entityId} />}
          </button>
        </div>

        <div className="hidden xl:flex">
          <Icons entityId={result.entityId} />
        </div>
      </div>
    </div>
  );
};

export default SearchResult;

const Icons = ({ entityId }: { entityId: string }) => {
  const [licenseModalOpen, setLicenseModalOpen] = useState<boolean>(false);

  const router = useRouter();

  const handleClick = (e: any, link: string) => {
    e.stopPropagation();
    router.push(link);
  };

  return (
    <>
      <div
        className="
        absolute bottom-10 right-0 ml-auto mt-2 flex flex-nowrap items-center gap-2 rounded border bg-white p-2 text-2xl shadow-lg
        xl:relative xl:bottom-0 xl:mt-0  xl:border-none xl:bg-transparent xl:shadow-none
      "
      >
        {/* <button
          className={`
            rounded-lg p-2 text-xl text-neutral-600 hover:bg-green-500
            hover:text-white
          `}
          onClick={(e) => {
            e.stopPropagation();
            setLicenseModalOpen(true);
          }}
        >
          <div className="flex flex-col items-center justify-center  gap-2">
            <AiFillPlusCircle />{" "}
            <span className="shrink-0 text-xs">License</span>
          </div>
        </button> */}
        {/* {[
          {
            link: ``,
            icon: <AiFillBell />,
            text: "Alerts",
            color: "hover:bg-yellow-500",
            notification: null,
          },
          {
            link: ``,
            icon: <MdFlag />,
            text: "Report",
            color: "hover:bg-red-500",
            notification: null,
          },
        ].map((item, index) => {
          return (
            <button
              key={index}
              className={`
                  p-2 rounded-lg text-neutral-600 text-xl hover:text-white
                  ${item.color}
                `}
              onClick={(e) => {
                e.stopPropagation();
                handleClick(e, item.link);
              }}
            >
              <div className="flex flex-col items-center justify-center  gap-2">
                {item.icon}{" "}
                <span className="text-xs shrink-0">{item.text}</span>
              </div>
            </button>
          );
        })} */}
        <AddLicense
          isOpen={licenseModalOpen}
          onClose={() => setLicenseModalOpen(false)}
          entityId={entityId}
        />
      </div>
    </>
  );
};
