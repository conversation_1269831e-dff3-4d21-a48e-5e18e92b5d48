import React from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'

const SearchTabBar = ({
  options,
  activeRadio,
  setActiveRadio,
  formMethods
}:{
  options: any;
  activeRadio: string | null;
  setActiveRadio: React.Dispatch<React.SetStateAction<string | null>>;
  formMethods: any;
}) => {
  
  const router = useRouter();
  return (
    <motion.div className='flex mb-6 border border-neutral-300 rounded overflow-hidden w-fit'>
      {options && options.map((option:any) => {
        return (
          <label key={option.value} 
            htmlFor={option.value} 
            className={`px-4 py-2 cursor-pointer text-sm ${activeRadio === option.value ? 'bg-neutral-300 text-black' : 'bg-gray-50 text-gray-700 border-r'}`}
          >
          <input
            value={option.value}
            type="radio"
            name="searchType"
            id={option.value}
            onChange={() => {
              setActiveRadio(option.value);
              router.push(`/search/entity`);
              formMethods.reset();
            }}
            className="hidden"
            checked={activeRadio === option.value}
          />
            {option.section}
          </label>
        )
      })}
    </motion.div>
  )
}

export default SearchTabBar
