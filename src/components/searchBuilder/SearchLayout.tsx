"use client";
import { useState, useEffect } from "react";
import { DynamicInputData } from "../forms/controls/dynamicControlTypes";
import SearchTitle from "./components/SearchTitle";
import { FormProvider, useForm } from "react-hook-form";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { DynamicControl } from "../forms/controls/DynamicControl";
import { useSearchParams } from "next/navigation";
import SearchResults from "./searchResults/SearchResults";
import { motion } from "framer-motion";
import HideSearchForm from "./components/HideSearchForm";
import Button from "../ui/buttons/Button";
import Modal from "../modal/Modal";
import NewEntity from "./components/NewEntity";
import { useSearchAddress } from "@/hooks/api/useSearch";
import PageContainer from "../ui/Page/PageContainer";
import LoadingSpinner from "../ui/LoadingSpinner";

import { FiUserPlus } from "react-icons/fi";

export type SearchFormValues = {
  section: string;
  value: string;
  columns?: {
    inputs: DynamicInputData[];
  }[];
};

type SearchParams = {
  [key: string]: string;
};

export function areAllFieldsEmpty(values: any) {
  return Object.values(values).every((value) => {
    if (typeof value === "string") {
      return value.trim() === "";
    }
    return !value;
  });
}

const SearchLayout = ({ formData }: { formData: SearchFormValues[] }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const entityType = useParams().entityType as string;

  let searchUrl: any = {};

  if (searchParams) {
    searchUrl = Object.fromEntries(Array.from(searchParams.entries()));
  }

  const formMethods = useForm({ defaultValues: searchUrl });

  const options = formData.map(({ section, value }) => ({ section, value }));
  const matchingOption = options.find((option) => option.value === entityType);
  const active = matchingOption ? matchingOption.value : options[0].value;

  const [search, setSearch] = useState<SearchParams>({});
  const [searchCollapse, setSearchCollapse] = useState<boolean>(false);
  const [modalOpen, setModalOpen] = useState<boolean>(false);

  console.log(entityType)

  const searchResult = useSearchAddress(entityType, search);

  console.log(searchResult)

  useEffect(() => {
    let newSearch: any = {};
    if (searchParams) {
      const filteredEntries = Array.from(searchParams.entries())
        .filter(([key, value]) => key !== "entityType" && value.trim() !== "")
        .map(([key, value]) => [key, value.trim()]);

      newSearch = Object.fromEntries(filteredEntries);
    }
    setSearch(newSearch);
  }, [searchParams]);

  const handleSubmit = formMethods.handleSubmit((data: any) => {
    let link = `/search/${entityType}?`;

    for (const [key, value] of Object.entries(data)) {
      if (value !== "" && key !== "entityType") link += `${key}=${value}&`;
    }

    return router.push(link);
  });

  const closeModal = () => {
    setModalOpen(false);
  };

  type Entity = {
    icon: string;
    title: string;
    description: string;
    link: string;
  };

  const createEntity: Entity[] = [
    {
      icon: "user",
      title: "Individual",
      description: "Create a new individual",
      link: `/onlineForm/new-resident-registration-clerk`,
    },
  ];

  const entityIconMap: { [key: string]: any } = {
    user: FiUserPlus,
  };

  const { watch } = formMethods;
  const watchedValues = watch();

  return (
    <div className="container mx-auto">
      {/* Search Container */}
      <motion.div className="relative m-6 rounded-lg border border-neutral-200 bg-white p-6 shadow-lg">
        {!searchCollapse && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <div>
              <SearchTitle label={"Search:"} />
               
              {/* Each Form */}
              {formData.map((form) => {
                if (form.value === entityType) {
                  const columns = form.columns || [];
                  return (
                    <FormProvider {...formMethods} key={form.value}>
                      <form
                        onSubmit={handleSubmit}
                        className="flex flex-col gap-10 overflow-x-hidden"
                      >
                        <div
                          key={form.value}
                          className="w-full gap-10 overflow-hidden lg:grid"
                          style={{
                            gridTemplateColumns: `repeat(${columns.length}, minmax(20rem, 1fr))`,
                          }}
                        >
                          {columns.map((column, index) => {
                            return (
                              <div key={index} className="">
                                {column.inputs.map((input) => {
                                  return (
                                    <div
                                      key={input.fieldName}
                                      className="my-1 flex items-center"
                                    >
                                      <label className="w-36 shrink-0 text-sm">
                                        {input.label}
                                      </label>
                                      <DynamicControl inputData={input} />
                                    </div>
                                  );
                                })}
                              </div>
                            );
                          })}
                        </div>

                        {/* Save and Clear Buttons */}
                        <div className="mt-6 flex w-full justify-end gap-4">
                          <Button
                            type="submit"
                            variant="primary"
                            disabled={areAllFieldsEmpty(watchedValues)}
                          >
                            Search
                          </Button>
                          <Button
                            type="button"
                            variant="secondary"
                            outline={true}
                            onClick={() => {
                              formMethods.reset();
                              router.push(
                                `/search/${entityType}`,
                              );
                            }}
                          >
                            Clear
                          </Button>
                        </div>
                      </form>
                    </FormProvider>
                  );
                }
              })}
            </div>
          </motion.div>
        )}
        <HideSearchForm
          searchCollapse={searchCollapse}
          setSearchCollapse={setSearchCollapse}
        />
      </motion.div>

      {/* Results Go Here*/}
      {areAllFieldsEmpty(search) ? null : searchResult?.isLoading ? (
        <div className="container mx-auto flex items-center justify-center">
          <LoadingSpinner />
        </div>
      ) : searchResult?.data?.items?.length === 0 ? (
        <div className="px-6 pb-6 text-red-500">
          <PageContainer>No Results Found</PageContainer>
        </div>
      ) : (
        <SearchResults results={searchResult?.data?.items} />
      )}

      {/* Modal */}
      <Modal
        isOpen={modalOpen}
        title="Create New Entity 🔗"
        onClose={closeModal}
        position="left"
      >
        <NewEntity entity={createEntity} />
      </Modal>
    </div>
  );
};

export default SearchLayout;
