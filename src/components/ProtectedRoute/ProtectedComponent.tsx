import { useMyProfile } from '@/hooks/providers/useMyProfile';
import React from 'react'

export default function ProtectedComponent({
  permissions,
  children
}:{
  permissions: string[];
  children: React.ReactNode;
}) {
  const { permissions: myPermissions } = useMyProfile();

  const hasPermission = permissions.some(permission => myPermissions?.includes(permission));

  if(hasPermission) {
    return <>{children}</>
  }

  return null
  
}
