"use client";
import React from "react";
import Button from "../ui/buttons/Button";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { useRouter } from "next/navigation";

export default function ProtectedRoute({
  children,
  permissions,
}: {
  children: React.ReactNode;
  permissions: string[];
}) {
  const { hasPermissions } = useMyProfile();


  if (hasPermissions(permissions)) {
    return <>{children}</>;
  }

  return <Warning message="You do not have permission to access this page." />;
}

const Warning = ({ message }: { message: string }) => {
  const { push } = useRouter();
  const { hasPermissions } = useMyProfile();

  return (
    <div className="flex flex-col items-center">
      <h1 className="px-6 py-10 text-center text-2xl">{message}</h1>
      {hasPermissions(["super-admin"]) && (
        <Button onClick={() => push("/dashboard")}>Go to Dashboard</Button>
      )}
      {hasPermissions(["resident"]) && (
        <Button onClick={() => push("/home")}>Go Home</Button>
      )}
    </div>
  );
};
