import { NextRequest, NextResponse } from "next/server";
import { getKeycloakConfig } from "@/utils/authConfig";

export function middleware(request: NextRequest) {
  const nonce = Buffer.from(crypto.randomUUID()).toString("base64");
  const backendUrl = process.env.NEXT_PUBLIC_API_BACKEND_HOST?.replace(
    "/api",
    "",
  );
  const keycloakUrl = getKeycloakConfig().url;
  const appEnvironment = process.env.NEXT_PUBLIC_APP_ENV ?? "default";
  const devScriptPolicy = ["unsafe-eval"]; // NextJS uses react-refresh in development
  const isDev = appEnvironment === "local" || appEnvironment === "default";

  const contentSecurityPolicyHeaderValue = `
  default-src 'none';
  script-src 'self' nonce-${nonce} 'strict-dynamic' ${isDev ? devScriptPolicy.join(" ") : ""};
  style-src 'self' nonce-${nonce};
  img-src 'self' blob: data:;
  font-src 'self';
  base-uri 'self';
  form-action 'self';
  connect-src 'self' ${backendUrl} ${keycloakUrl};
  frame-src 'self' ${keycloakUrl};
  ${isDev ? "" : "upgrade-insecure-requests;"}
`
    .replace(/\s{2,}/g, " ")
    .trim();

  const permissionPolicyHeaderValue = `
autoplay=(), 
bluetooth=(), 
camera=(self), 
clipboard-read=(self), 
clipboard-write=(self), 
display-capture=(self), 
encrypted-media=(self), 
fullscreen=(self), 
gamepad=(), 
geolocation=(self), 
gyroscope=(), 
hid=(), 
idle-detection=(), 
keyboard-map=(), 
local-fonts=(), 
magnetometer=(), 
microphone=(), 
midi=(), 
payment=(), 
picture-in-picture=(self), 
publickey-credentials-get=(), 
screen-wake-lock=(), 
usb=()
`
    .replace(/\s{2,}/g, " ")
    .trim();

  const requestHeaders = new Headers(request.headers);
  requestHeaders.set("x-nonce", nonce);

  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });

  // DR: remove below for security purposes
  //response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT,DELETE, OPTIONS",
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, X-Requested-With",
  );
  response.headers.set("Access-Control-Allow-Credentials", "true");
  response.headers.set("Access-Control-Max-Age", "86400");

  //TODO: come back to this
  //response.headers.set('Content-Security-Policy', contentSecurityPolicyHeaderValue);
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("X-Frame-Options", "sameorigin");
  response.headers.set("X-XSS-Protection", "1; mode=block");
  response.headers.set("Referrer-Policy", "no-referrer");
  response.headers.set("Permissions-Policy", permissionPolicyHeaderValue);

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    {
      source: "/((?!api|_next/static|_next/image|favicon.ico).*)",
      missing: [
        { type: "header", key: "next-router-prefetch" },
        { type: "header", key: "purpose", value: "prefetch" },
      ],
    },
  ],
};
